import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, Platform } from 'react-native';
import {
  Text,
  Appbar,
  List,
  Switch,
  useTheme,
  Divider,
  Button,
  Dialog,
  Portal,
  TextInput,
  Modal,
} from 'react-native-paper';
import DateTimePicker from '@react-native-community/datetimepicker';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { router } from 'expo-router';

import { AppSettings } from '../../src/types/app';
import { storageService } from '../../src/services/storage';
import { habitService } from '../../src/services/habitService';
import { useAppTheme } from '../../src/components/ui/ThemeProvider';
import { STORAGE_KEYS } from '../../src/types/app';

export default function SettingsScreen() {
  const theme = useTheme();
  const { isDark, toggleTheme, themeMode, setThemeMode } = useAppTheme();
  const [settings, setSettings] = useState<AppSettings>({
    theme: 'system',
    language: 'en',
    notificationsEnabled: true,
    defaultReminderTime: '09:00',
    isPremium: false,
    onboardingCompleted: false,
  });
  const [loading, setLoading] = useState(true);
  const [clearDataDialogVisible, setClearDataDialogVisible] = useState(false);
  const [themeDialogVisible, setThemeDialogVisible] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [tempTime, setTempTime] = useState(new Date());

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await storageService.getSecureItem<AppSettings>(STORAGE_KEYS.SETTINGS);
      if (savedSettings) {
        setSettings(savedSettings);
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateSettings = async (newSettings: Partial<AppSettings>) => {
    try {
      const updatedSettings = { ...settings, ...newSettings };
      await storageService.setSecureItem(STORAGE_KEYS.SETTINGS, updatedSettings);
      setSettings(updatedSettings);
    } catch (error) {
      console.error('Failed to update settings:', error);
    }
  };

  const handleNotificationsToggle = () => {
    updateSettings({ notificationsEnabled: !settings.notificationsEnabled });
  };

  const handleClearAllData = () => {
    Alert.alert(
      'Clear All Data',
      'This will permanently delete all your habits, completions, and settings. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear Data',
          style: 'destructive',
          onPress: async () => {
            try {
              await storageService.clearAllData();
              // Reset to initial state
              setSettings({
                theme: 'system',
                language: 'en',
                notificationsEnabled: true,
                defaultReminderTime: '09:00',
                isPremium: false,
                onboardingCompleted: false,
              });
              Alert.alert('Success', 'All data has been cleared.');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear data. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleExportData = async () => {
    try {
      const data = await storageService.exportData();
      // In a real app, you would use expo-sharing or similar to share the data
      Alert.alert(
        'Export Data',
        'Data export feature will be available in the premium version.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to export data. Please try again.');
    }
  };

  const handleTimePickerOpen = () => {
    const [hours, minutes] = settings.defaultReminderTime.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    setTempTime(date);
    setShowTimePicker(true);
  };

  const handleTimeConfirm = () => {
    const timeString = tempTime.toTimeString().slice(0, 5);
    const newSettings = { ...settings, defaultReminderTime: timeString };
    setSettings(newSettings);
    saveSettings(newSettings);
    setShowTimePicker(false);
  };

  const handleTimeCancel = () => {
    setShowTimePicker(false);
  };

  const handleWebTimeChange = (timeString: string) => {
    const newSettings = { ...settings, defaultReminderTime: timeString };
    setSettings(newSettings);
    saveSettings(newSettings);
  };

  const handleUpgradeToPremium = () => {
    router.push('/premium');
  };

  const getThemeDisplayName = (mode: string) => {
    switch (mode) {
      case 'light': return 'Light';
      case 'dark': return 'Dark';
      case 'system': return 'System';
      default: return 'System';
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Appbar.Header style={{ backgroundColor: theme.colors.surface, elevation: 4 }}>
          <Appbar.Content title="⚙️ Settings" titleStyle={{ fontWeight: '700', fontSize: 20 }} />
        </Appbar.Header>
        <View style={styles.loadingContainer}>
          <Text variant="bodyLarge" style={{ color: theme.colors.onSurfaceVariant }}>
            Loading settings...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Appbar.Header style={{ backgroundColor: theme.colors.surface, elevation: 4 }}>
        <Appbar.Content
          title="⚙️ Settings"
          titleStyle={{ fontWeight: '700', fontSize: 20 }}
        />
      </Appbar.Header>

      <ScrollView style={styles.scrollView}>
        {/* Appearance Section */}
        <List.Section>
          <List.Subheader style={{ fontWeight: '700', fontSize: 16 }}>🎨 Appearance</List.Subheader>
          <List.Item
            title="Theme"
            description={`Currently using ${getThemeDisplayName(themeMode)} theme`}
            left={(props) => <List.Icon {...props} icon="palette" />}
            right={() => (
              <Text variant="bodyMedium" style={{ color: theme.colors.primary }}>
                {getThemeDisplayName(themeMode)}
              </Text>
            )}
            onPress={() => setThemeDialogVisible(true)}
          />
        </List.Section>

        <Divider />

        {/* Notifications Section */}
        <List.Section>
          <List.Subheader style={{ fontWeight: '700', fontSize: 16 }}>🔔 Notifications</List.Subheader>
          <List.Item
            title="Enable Notifications"
            description="Receive daily reminders for your habits"
            left={(props) => <List.Icon {...props} icon="bell" />}
            right={() => (
              <Switch
                value={settings.notificationsEnabled}
                onValueChange={handleNotificationsToggle}
              />
            )}
          />
          <List.Item
            title="Default Reminder Time"
            description={`Reminders will be sent at ${settings.defaultReminderTime}`}
            left={(props) => <List.Icon {...props} icon="clock" />}
            disabled={!settings.notificationsEnabled}
            onPress={handleTimePickerOpen}
          />
        </List.Section>

        <Divider />

        {/* Premium Section */}
        <List.Section>
          <List.Subheader style={{ fontWeight: '700', fontSize: 16 }}>👑 Premium</List.Subheader>
          {settings.isPremium ? (
            <List.Item
              title="Premium Active"
              description="Thank you for supporting Habiit!"
              left={(props) => <List.Icon {...props} icon="crown" />}
              right={() => (
                <MaterialCommunityIcons 
                  name="check-circle" 
                  size={24} 
                  color={theme.colors.primary} 
                />
              )}
            />
          ) : (
            <List.Item
              title="Upgrade to Premium"
              description="Unlock unlimited habits and advanced features"
              left={(props) => <List.Icon {...props} icon="crown" />}
              right={() => (
                <MaterialCommunityIcons 
                  name="chevron-right" 
                  size={24} 
                  color={theme.colors.onSurfaceVariant} 
                />
              )}
              onPress={handleUpgradeToPremium}
            />
          )}
        </List.Section>

        <Divider />

        {/* Data & Privacy Section */}
        <List.Section>
          <List.Subheader style={{ fontWeight: '700', fontSize: 16 }}>🔒 Data & Privacy</List.Subheader>
          <List.Item
            title="Privacy Policy"
            description="Learn how we protect your data"
            left={(props) => <List.Icon {...props} icon="shield-check" />}
            right={() => (
              <MaterialCommunityIcons 
                name="chevron-right" 
                size={24} 
                color={theme.colors.onSurfaceVariant} 
              />
            )}
            onPress={() => {
              Alert.alert(
                'Privacy First',
                'Habiit stores all your data locally on your device. No data is sent to our servers without your explicit consent.',
                [{ text: 'OK' }]
              );
            }}
          />
          <List.Item
            title="Export Data"
            description="Download your habit data"
            left={(props) => <List.Icon {...props} icon="download" />}
            onPress={handleExportData}
          />
          <List.Item
            title="Clear All Data"
            description="Permanently delete all your data"
            left={(props) => <List.Icon {...props} icon="delete" />}
            onPress={handleClearAllData}
          />
        </List.Section>

        <Divider />

        {/* About Section */}
        <List.Section>
          <List.Subheader style={{ fontWeight: '700', fontSize: 16 }}>ℹ️ About</List.Subheader>
          <List.Item
            title="Version"
            description="1.0.0"
            left={(props) => <List.Icon {...props} icon="information" />}
          />
          <List.Item
            title="Support"
            description="Get help and send feedback"
            left={(props) => <List.Icon {...props} icon="help-circle" />}
            onPress={() => {
              Alert.alert(
                'Support',
                'For support and feedback, please contact <NAME_EMAIL>',
                [{ text: 'OK' }]
              );
            }}
          />
        </List.Section>
      </ScrollView>

      {/* Theme Selection Dialog */}
      <Portal>
        <Dialog visible={themeDialogVisible} onDismiss={() => setThemeDialogVisible(false)}>
          <Dialog.Title>Choose Theme</Dialog.Title>
          <Dialog.Content>
            <List.Item
              title="Light"
              left={() => <MaterialCommunityIcons name="white-balance-sunny" size={24} />}
              right={() => themeMode === 'light' ? <MaterialCommunityIcons name="check" size={24} color={theme.colors.primary} /> : null}
              onPress={() => {
                setThemeMode('light');
                setThemeDialogVisible(false);
              }}
            />
            <List.Item
              title="Dark"
              left={() => <MaterialCommunityIcons name="moon-waning-crescent" size={24} />}
              right={() => themeMode === 'dark' ? <MaterialCommunityIcons name="check" size={24} color={theme.colors.primary} /> : null}
              onPress={() => {
                setThemeMode('dark');
                setThemeDialogVisible(false);
              }}
            />
            <List.Item
              title="System"
              left={() => <MaterialCommunityIcons name="cog" size={24} />}
              right={() => themeMode === 'system' ? <MaterialCommunityIcons name="check" size={24} color={theme.colors.primary} /> : null}
              onPress={() => {
                setThemeMode('system');
                setThemeDialogVisible(false);
              }}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setThemeDialogVisible(false)}>Cancel</Button>
          </Dialog.Actions>
        </Dialog>

        {/* Time Picker */}
        {Platform.OS === 'web' ? (
          <Modal
            visible={showTimePicker}
            onDismiss={handleTimeCancel}
            contentContainerStyle={{
              backgroundColor: theme.colors.surface,
              padding: 20,
              margin: 20,
              borderRadius: 16,
            }}
          >
            <Text variant="titleMedium" style={{ marginBottom: 16, textAlign: 'center' }}>
              🕐 Set Default Reminder Time
            </Text>
            <TextInput
              mode="outlined"
              value={settings.defaultReminderTime}
              onChangeText={handleWebTimeChange}
              placeholder="09:00"
              style={{ marginBottom: 16 }}
              left={<TextInput.Icon icon="clock" />}
            />
            <Button mode="contained" onPress={handleTimeCancel}>
              Done
            </Button>
          </Modal>
        ) : (
          showTimePicker && (
            <DateTimePicker
              value={tempTime}
              mode="time"
              is24Hour={true}
              display="default"
              onChange={(event, selectedTime) => {
                if (event.type === 'set' && selectedTime) {
                  setTempTime(selectedTime);
                  handleTimeConfirm();
                } else {
                  handleTimeCancel();
                }
              }}
            />
          )
        )}
      </Portal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
