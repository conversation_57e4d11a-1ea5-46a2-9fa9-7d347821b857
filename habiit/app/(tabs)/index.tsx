import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl } from 'react-native';
import {
  Text,
  FAB,
  Appbar,
  Chip,
  useTheme,
  Snackbar,
} from 'react-native-paper';
import { useFocusEffect, router } from 'expo-router';
import { format, isToday, startOfDay } from 'date-fns';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Habit } from '../../src/types/habit';
import { habitService } from '../../src/services/habitService';
import { HabitCard } from '../../src/components/habit/HabitCard';
import { ProgressBar } from '../../src/components/ui/ProgressBar';

export default function HomeScreen() {
  const theme = useTheme();
  const [habits, setHabits] = useState<Habit[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [completionStats, setCompletionStats] = useState({ completed: 0, total: 0 });
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  useFocusEffect(
    useCallback(() => {
      loadHabits();
    }, [selectedDate])
  );

  const loadHabits = async () => {
    try {
      setLoading(true);
      const activeHabits = await habitService.getActiveHabits();
      setHabits(activeHabits);

      // Calculate completion stats for selected date
      let completed = 0;
      for (const habit of activeHabits) {
        const isCompleted = await habitService.isHabitCompleted(habit.id, selectedDate);
        if (isCompleted) completed++;
      }

      setCompletionStats({ completed, total: activeHabits.length });
    } catch (error) {
      console.error('Failed to load habits:', error);
      showSnackbar('Failed to load habits');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHabits();
    setRefreshing(false);
  };

  const showSnackbar = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  const handleCreateHabit = () => {
    router.push('/habit/create');
  };

  const handleEditHabit = (habitId: string) => {
    router.push(`/habit/edit?habitId=${habitId}`);
  };

  const handleCompletionChange = async (habitId: string, completed: boolean) => {
    // Refresh completion stats immediately
    await loadHabits();
  };

  const completionRate = completionStats.total > 0 ? completionStats.completed / completionStats.total : 0;
  const isCurrentDate = isToday(selectedDate);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Appbar.Header style={{ backgroundColor: theme.colors.surface, elevation: 4 }}>
        <Appbar.Content
          title={isCurrentDate ? "🌟 Today" : `📅 ${format(selectedDate, 'MMM d, yyyy')}`}
          titleStyle={{ fontWeight: '700', fontSize: 20 }}
        />
        <Appbar.Action
          icon="chevron-left"
          iconColor={theme.colors.primary}
          onPress={() => {
            // Navigate to previous day
            const previousDay = new Date(selectedDate);
            previousDay.setDate(previousDay.getDate() - 1);
            setSelectedDate(previousDay);
            showSnackbar(`📅 Viewing ${format(previousDay, 'MMM d, yyyy')}`);
          }}
        />
        <Appbar.Action
          icon="chevron-right"
          iconColor={theme.colors.primary}
          onPress={() => {
            // Navigate to next day (but not future)
            const nextDay = new Date(selectedDate);
            nextDay.setDate(nextDay.getDate() + 1);
            const today = new Date();
            if (nextDay <= today) {
              setSelectedDate(nextDay);
              showSnackbar(`📅 Viewing ${format(nextDay, 'MMM d, yyyy')}`);
            } else {
              showSnackbar('🚫 Cannot view future dates');
            }
          }}
        />
        <Appbar.Action
          icon="plus"
          iconColor={theme.colors.primary}
          onPress={handleCreateHabit}
        />
      </Appbar.Header>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Daily Progress Summary */}
        <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.summaryHeader}>
            <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
              {isCurrentDate ? "🎯 Today's Progress" : "📊 Daily Progress"}
            </Text>
            <Chip
              mode="flat"
              compact
              style={[
                styles.progressChip,
                {
                  backgroundColor: completionRate >= 1 ? '#06D6A020' : '#7C3AED20',
                  borderColor: completionRate >= 1 ? '#06D6A0' : '#7C3AED',
                  borderWidth: 2,
                }
              ]}
              textStyle={{
                color: completionRate >= 1 ? '#06D6A0' : '#7C3AED',
                fontWeight: '800',
                fontSize: 14,
                textAlign: 'center',
                lineHeight: 18,
              }}
            >
              {completionRate >= 1 ? '🏆' : completionRate >= 0.5 ? '⚡' : '🎯'} {completionStats.completed}/{completionStats.total}
            </Chip>
          </View>

          <View style={styles.progressBarContainer}>
            <ProgressBar
              progress={completionRate}
              height={16}
              showPercentage={false}
              animated={true}
              style={styles.progressBar}
              color={completionRate >= 1 ? '#06D6A0' : '#7C3AED'}
            />
            <Text style={[
              styles.progressPercentage,
              { color: completionRate >= 1 ? '#06D6A0' : '#7C3AED' }
            ]}>
              {Math.round(completionRate * 100)}%
            </Text>
          </View>

          {completionStats.total > 0 && (
            <Text
              variant="bodyMedium"
              style={[
                styles.summaryText,
                {
                  color: theme.colors.onSurfaceVariant,
                  textAlign: 'center',
                  fontWeight: '600',
                }
              ]}
            >
              {completionRate === 1
                ? "🎉 Perfect day! All habits completed! 🌟"
                : completionRate >= 0.8
                ? "🔥 Amazing progress! Keep it up! ⚡"
                : completionRate >= 0.5
                ? "💪 Great start! You're halfway there! 🎯"
                : "🌱 Every step counts! Let's build momentum! ✨"
              }
            </Text>
          )}
        </View>

        {/* Habits List */}
        <View style={styles.habitsSection}>
          <Text
            variant="titleMedium"
            style={[styles.sectionTitle, { color: theme.colors.onSurface, fontWeight: '700' }]}
          >
            {isCurrentDate ? "🏆 Today's Habits" : "📋 Habits"}
          </Text>

          {loading ? (
            <View style={styles.emptyState}>
              <Text variant="bodyLarge" style={{ color: theme.colors.onSurfaceVariant }}>
                ⏳ Loading your awesome habits...
              </Text>
            </View>
          ) : habits.length === 0 ? (
            <View style={styles.emptyState}>
              <Text variant="headlineSmall" style={{ color: theme.colors.onSurface, marginBottom: 12, textAlign: 'center' }}>
                🌱 Ready to grow?
              </Text>
              <Text variant="bodyLarge" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', lineHeight: 24 }}>
                Create your first habit and start building amazing routines! ✨
              </Text>
              <Button
                mode="contained"
                onPress={handleCreateHabit}
                style={{ marginTop: 20, borderRadius: 25 }}
                contentStyle={{ paddingVertical: 8 }}
              >
                🚀 Create First Habit
              </Button>
            </View>
          ) : (
            <View style={styles.habitsList}>
              {habits.map((habit) => (
                <HabitCard
                  key={habit.id}
                  habit={habit}
                  date={selectedDate}
                  onPress={() => handleEditHabit(habit.id)}
                  onLongPress={() => handleEditHabit(habit.id)}
                  onCompletionChange={handleCompletionChange}
                />
              ))}
            </View>
          )}
        </View>
      </ScrollView>

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleCreateHabit}
        label={habits.length === 0 ? "Create Habit" : undefined}
      />

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100, // Space for FAB
  },
  summaryCard: {
    padding: 20,
    borderRadius: 20,
    marginBottom: 28,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 18,
  },
  progressChip: {
    height: 36,
    borderRadius: 18,
    elevation: 3,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  progressBarContainer: {
    position: 'relative',
    marginVertical: 16,
  },
  progressBar: {
    borderRadius: 8,
    backgroundColor: '#E5E7EB',
  },
  progressPercentage: {
    position: 'absolute',
    right: 8,
    top: -2,
    fontSize: 12,
    fontWeight: '800',
    textAlign: 'center',
    lineHeight: 20,
  },
  summaryText: {
    marginTop: 16,
    textAlign: 'center',
    fontSize: 15,
    lineHeight: 22,
  },
  habitsSection: {
    flex: 1,
  },
  sectionTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  habitsList: {
    gap: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  fab: {
    position: 'absolute',
    margin: 20,
    right: 0,
    bottom: 0,
    borderRadius: 28,
    elevation: 6,
  },
});
