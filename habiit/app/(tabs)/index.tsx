import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl } from 'react-native';
import {
  Text,
  FAB,
  Appbar,
  Chip,
  useTheme,
  Snackbar,
} from 'react-native-paper';
import { useFocusEffect, router } from 'expo-router';
import { format, isToday, startOfDay } from 'date-fns';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Habit } from '../../src/types/habit';
import { habitService } from '../../src/services/habitService';
import { HabitCard } from '../../src/components/habit/HabitCard';
import { ProgressBar } from '../../src/components/ui/ProgressBar';

export default function HomeScreen() {
  const theme = useTheme();
  const [habits, setHabits] = useState<Habit[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [completionStats, setCompletionStats] = useState({ completed: 0, total: 0 });
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  useFocusEffect(
    useCallback(() => {
      loadHabits();
    }, [selectedDate])
  );

  const loadHabits = async () => {
    try {
      setLoading(true);
      const activeHabits = await habitService.getActiveHabits();
      setHabits(activeHabits);

      // Calculate completion stats for selected date
      let completed = 0;
      for (const habit of activeHabits) {
        const isCompleted = await habitService.isHabitCompleted(habit.id, selectedDate);
        if (isCompleted) completed++;
      }

      setCompletionStats({ completed, total: activeHabits.length });
    } catch (error) {
      console.error('Failed to load habits:', error);
      showSnackbar('Failed to load habits');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHabits();
    setRefreshing(false);
  };

  const showSnackbar = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  const handleCreateHabit = () => {
    router.push('/habit/create');
  };

  const handleEditHabit = (habitId: string) => {
    router.push(`/habit/edit?habitId=${habitId}`);
  };

  const handleCompletionChange = async (habitId: string, completed: boolean) => {
    // Refresh completion stats immediately
    await loadCompletionStats();
  };

  const completionRate = completionStats.total > 0 ? completionStats.completed / completionStats.total : 0;
  const isCurrentDate = isToday(selectedDate);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Appbar.Header style={{ backgroundColor: theme.colors.surface }}>
        <Appbar.Content
          title={isCurrentDate ? "Today" : format(selectedDate, 'MMMM d, yyyy')}
          titleStyle={{ fontWeight: '600' }}
        />
        <Appbar.Action
          icon="calendar"
          onPress={() => {
            // TODO: Open date picker
            showSnackbar('Date picker coming soon!');
          }}
        />
      </Appbar.Header>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Daily Progress Summary */}
        <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.summaryHeader}>
            <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
              {isCurrentDate ? "Today's Progress" : "Daily Progress"}
            </Text>
            <Chip
              mode="outlined"
              compact
              style={[styles.progressChip, { borderColor: theme.colors.primary }]}
              textStyle={{ color: theme.colors.primary, fontWeight: '600' }}
            >
              {completionStats.completed}/{completionStats.total}
            </Chip>
          </View>

          <ProgressBar
            progress={completionRate}
            height={12}
            showPercentage={true}
            animated={true}
          />

          {completionStats.total > 0 && (
            <Text
              variant="bodySmall"
              style={[styles.summaryText, { color: theme.colors.onSurfaceVariant }]}
            >
              {completionRate === 1
                ? "🎉 All habits completed!"
                : `${completionStats.completed} of ${completionStats.total} habits completed`
              }
            </Text>
          )}
        </View>

        {/* Habits List */}
        <View style={styles.habitsSection}>
          <Text
            variant="titleMedium"
            style={[styles.sectionTitle, { color: theme.colors.onSurface }]}
          >
            {isCurrentDate ? "Today's Habits" : "Habits"}
          </Text>

          {loading ? (
            <View style={styles.emptyState}>
              <Text variant="bodyLarge" style={{ color: theme.colors.onSurfaceVariant }}>
                Loading habits...
              </Text>
            </View>
          ) : habits.length === 0 ? (
            <View style={styles.emptyState}>
              <Text variant="headlineSmall" style={{ color: theme.colors.onSurface, marginBottom: 8 }}>
                No habits yet
              </Text>
              <Text variant="bodyLarge" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center' }}>
                Create your first habit to start building better routines
              </Text>
            </View>
          ) : (
            <View style={styles.habitsList}>
              {habits.map((habit) => (
                <HabitCard
                  key={habit.id}
                  habit={habit}
                  date={selectedDate}
                  onPress={() => handleEditHabit(habit.id)}
                  onLongPress={() => handleEditHabit(habit.id)}
                  onCompletionChange={handleCompletionChange}
                />
              ))}
            </View>
          )}
        </View>
      </ScrollView>

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleCreateHabit}
        label={habits.length === 0 ? "Create Habit" : undefined}
      />

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100, // Space for FAB
  },
  summaryCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    elevation: 2,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressChip: {
    height: 28,
  },
  summaryText: {
    marginTop: 8,
    textAlign: 'center',
  },
  habitsSection: {
    flex: 1,
  },
  sectionTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  habitsList: {
    gap: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});
