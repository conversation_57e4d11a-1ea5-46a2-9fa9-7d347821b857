import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, Platform } from 'react-native';
import {
  Text,
  TextInput,
  Button,
  useTheme,
  Chip,
  Switch,
  List,
  Divider,
  Portal,
  Modal,
} from 'react-native-paper';
import DateTimePicker from '@react-native-community/datetimepicker';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { HabitFormData, HABIT_COLORS, HabitFrequency } from '../../src/types/habit';
import { habitService } from '../../src/services/habitService';

const EMOJI_OPTIONS = ['💪', '📚', '🏃', '💧', '🧘', '🎯', '🌱', '✍️', '🎵', '🍎'];
const DAYS_OF_WEEK = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export default function CreateHabitScreen() {
  const theme = useTheme();
  const [formData, setFormData] = useState<HabitFormData>({
    title: '',
    emoji: '',
    color: HABIT_COLORS[0],
    frequency: 'daily',
    customDays: [],
    reminderTime: '09:00',
    reminderEnabled: false,
    dailyTarget: 1,
    reminderInterval: 60,
    reminderTimes: [],
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [tempTime, setTempTime] = useState(new Date());
  const [editingTimeIndex, setEditingTimeIndex] = useState<number | null>(null);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Habit title is required';
    }

    if (formData.frequency === 'custom' && (!formData.customDays || formData.customDays.length === 0)) {
      newErrors.customDays = 'Please select at least one day';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    console.log('handleSave called with formData:', formData);

    if (!validateForm()) {
      console.log('Form validation failed');
      return;
    }

    setLoading(true);
    try {
      console.log('Creating habit with data:', formData);
      const newHabit = await habitService.createHabit(formData);
      console.log('Habit created successfully:', newHabit);

      Alert.alert('Success', 'Habit created successfully!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      console.error('Failed to create habit:', error);
      Alert.alert('Error', `Failed to create habit: ${error.message || error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const toggleCustomDay = (dayIndex: number) => {
    const currentDays = formData.customDays || [];
    const newDays = currentDays.includes(dayIndex)
      ? currentDays.filter(d => d !== dayIndex)
      : [...currentDays, dayIndex].sort();

    setFormData({ ...formData, customDays: newDays });
  };

  const handleTimePickerOpen = () => {
    // Parse current time or use default
    const timeString = formData.reminderTime || '09:00';
    const [hours, minutes] = timeString.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    setTempTime(date);
    setShowTimePicker(true);
  };

  const handleTimeChange = (event: any, selectedTime?: Date) => {
    if (Platform.OS === 'android') {
      setShowTimePicker(false);
    }

    if (selectedTime) {
      setTempTime(selectedTime);
      if (Platform.OS === 'android') {
        const timeString = selectedTime.toTimeString().slice(0, 5);
        setFormData({ ...formData, reminderTime: timeString });
      }
    }
  };

  const handleTimeConfirm = () => {
    const timeString = tempTime.toTimeString().slice(0, 5);

    if (editingTimeIndex !== null) {
      // Editing a specific time in multiple daily habit
      const newTimes = [...(formData.reminderTimes || [])];
      newTimes[editingTimeIndex] = timeString;
      setFormData({ ...formData, reminderTimes: newTimes });
      setEditingTimeIndex(null);
    } else {
      // Regular reminder time
      setFormData({ ...formData, reminderTime: timeString });
    }

    setShowTimePicker(false);
  };

  const handleTimeCancel = () => {
    setShowTimePicker(false);
  };

  const handleWebTimeChange = (timeString: string) => {
    setFormData({ ...formData, reminderTime: timeString });
  };

  const handleEditSpecificTime = (index: number, currentTime: string) => {
    const [hours, minutes] = currentTime.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    setTempTime(date);
    setEditingTimeIndex(index);
    setShowTimePicker(true);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Title Input */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Habit Details
          </Text>
          <TextInput
            label="Habit Title"
            value={formData.title}
            onChangeText={(text) => setFormData({ ...formData, title: text })}
            mode="outlined"
            error={!!errors.title}
            style={styles.textInput}
          />
          {errors.title && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
              {errors.title}
            </Text>
          )}
        </View>

        {/* Emoji Selection */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Choose an Icon
          </Text>
          <View style={styles.emojiGrid}>
            {EMOJI_OPTIONS.map((emoji) => (
              <Chip
                key={emoji}
                mode={formData.emoji === emoji ? 'flat' : 'outlined'}
                selected={formData.emoji === emoji}
                onPress={() => setFormData({ ...formData, emoji })}
                style={styles.emojiChip}
              >
                {emoji}
              </Chip>
            ))}
          </View>
        </View>

        {/* Color Selection */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Choose a Color
          </Text>
          <View style={styles.colorGrid}>
            {HABIT_COLORS.map((color) => (
              <Chip
                key={color}
                mode={formData.color === color ? 'flat' : 'outlined'}
                selected={formData.color === color}
                onPress={() => setFormData({ ...formData, color })}
                style={[
                  styles.colorChip,
                  { 
                    backgroundColor: formData.color === color ? color : 'transparent',
                    borderColor: color,
                  }
                ]}
                textStyle={{ 
                  color: formData.color === color ? '#FFFFFF' : color 
                }}
              >
                {formData.color === color ? '✓' : ' '}
              </Chip>
            ))}
          </View>
        </View>

        {/* Frequency Selection */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Frequency
          </Text>
          
          <List.Item
            title="Daily"
            description="Every day"
            left={() => <MaterialCommunityIcons name="calendar-today" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <MaterialCommunityIcons 
                name={formData.frequency === 'daily' ? 'radiobox-marked' : 'radiobox-blank'} 
                size={24} 
                color={theme.colors.primary} 
              />
            )}
            onPress={() => setFormData({ ...formData, frequency: 'daily' })}
          />
          
          <List.Item
            title="Weekly"
            description="Once a week"
            left={() => <MaterialCommunityIcons name="calendar-week" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <MaterialCommunityIcons 
                name={formData.frequency === 'weekly' ? 'radiobox-marked' : 'radiobox-blank'} 
                size={24} 
                color={theme.colors.primary} 
              />
            )}
            onPress={() => setFormData({ ...formData, frequency: 'weekly' })}
          />
          
          <List.Item
            title="Custom Days"
            description="Choose specific days"
            left={() => <MaterialCommunityIcons name="calendar-multiple" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <MaterialCommunityIcons
                name={formData.frequency === 'custom' ? 'radiobox-marked' : 'radiobox-blank'}
                size={24}
                color={theme.colors.primary}
              />
            )}
            onPress={() => setFormData({ ...formData, frequency: 'custom' })}
          />

          <List.Item
            title="Multiple Times Daily"
            description="Set a daily target (e.g., drink water 8 times)"
            left={() => <MaterialCommunityIcons name="repeat" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <MaterialCommunityIcons
                name={formData.frequency === 'multiple_daily' ? 'radiobox-marked' : 'radiobox-blank'}
                size={24}
                color={theme.colors.primary}
              />
            )}
            onPress={() => setFormData({ ...formData, frequency: 'multiple_daily' })}
          />

          {formData.frequency === 'custom' && (
            <View style={styles.customDaysContainer}>
              <View style={styles.daysGrid}>
                {DAYS_OF_WEEK.map((day, index) => (
                  <Chip
                    key={day}
                    mode={formData.customDays?.includes(index) ? 'flat' : 'outlined'}
                    selected={formData.customDays?.includes(index)}
                    onPress={() => toggleCustomDay(index)}
                    style={styles.dayChip}
                    compact
                  >
                    {day}
                  </Chip>
                ))}
              </View>
              {errors.customDays && (
                <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
                  {errors.customDays}
                </Text>
              )}
            </View>
          )}

          {formData.frequency === 'multiple_daily' && (
            <View style={styles.multipleDailyContainer}>
              <Text variant="titleSmall" style={[styles.subsectionTitle, { color: theme.colors.onSurface }]}>
                Daily Target
              </Text>
              <View style={styles.targetSelector}>
                <Button
                  mode="outlined"
                  onPress={() => setFormData({ ...formData, dailyTarget: Math.max(1, (formData.dailyTarget || 1) - 1) })}
                  style={styles.targetButton}
                  disabled={(formData.dailyTarget || 1) <= 1}
                >
                  -
                </Button>
                <Text variant="headlineSmall" style={[styles.targetText, { color: theme.colors.primary }]}>
                  {formData.dailyTarget || 1}
                </Text>
                <Button
                  mode="outlined"
                  onPress={() => setFormData({ ...formData, dailyTarget: Math.min(20, (formData.dailyTarget || 1) + 1) })}
                  style={styles.targetButton}
                  disabled={(formData.dailyTarget || 1) >= 20}
                >
                  +
                </Button>
              </View>
              <Text variant="bodySmall" style={[styles.targetDescription, { color: theme.colors.onSurfaceVariant }]}>
                Complete this habit {formData.dailyTarget || 1} time{(formData.dailyTarget || 1) > 1 ? 's' : ''} per day
              </Text>

              <Text variant="titleSmall" style={[styles.subsectionTitle, { color: theme.colors.onSurface, marginTop: 16 }]}>
                Reminder Style
              </Text>
              <View style={styles.reminderStyleContainer}>
                <Chip
                  mode={!formData.reminderInterval ? 'flat' : 'outlined'}
                  selected={!formData.reminderInterval}
                  onPress={() => setFormData({ ...formData, reminderInterval: undefined, reminderTimes: [] })}
                  style={styles.reminderStyleChip}
                >
                  Specific Times
                </Chip>
                <Chip
                  mode={formData.reminderInterval ? 'flat' : 'outlined'}
                  selected={!!formData.reminderInterval}
                  onPress={() => setFormData({ ...formData, reminderInterval: 60, reminderTimes: [] })}
                  style={styles.reminderStyleChip}
                >
                  Interval
                </Chip>
              </View>

              {formData.reminderInterval && (
                <View style={styles.intervalContainer}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                    Remind every {formData.reminderInterval} minutes
                  </Text>
                  <View style={styles.intervalSelector}>
                    {[15, 30, 60, 120, 180].map((interval) => (
                      <Chip
                        key={interval}
                        mode={formData.reminderInterval === interval ? 'flat' : 'outlined'}
                        selected={formData.reminderInterval === interval}
                        onPress={() => setFormData({ ...formData, reminderInterval: interval })}
                        style={styles.intervalChip}
                        compact
                      >
                        {interval < 60 ? `${interval}m` : `${interval / 60}h`}
                      </Chip>
                    ))}
                  </View>
                </View>
              )}

              {!formData.reminderInterval && (
                <View style={styles.specificTimesContainer}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, marginBottom: 8 }}>
                    Specific reminder times
                  </Text>

                  {/* Display current specific times */}
                  <View style={styles.specificTimesList}>
                    {(formData.reminderTimes || []).map((time, index) => (
                      <View key={index} style={styles.specificTimeItem}>
                        <Button
                          mode="text"
                          onPress={() => handleEditSpecificTime(index, time)}
                          style={styles.timeEditButton}
                        >
                          <Text variant="bodyMedium" style={{ color: theme.colors.primary }}>
                            {time}
                          </Text>
                        </Button>
                        <Button
                          mode="text"
                          compact
                          onPress={() => {
                            const newTimes = [...(formData.reminderTimes || [])];
                            newTimes.splice(index, 1);
                            setFormData({ ...formData, reminderTimes: newTimes });
                          }}
                        >
                          Remove
                        </Button>
                      </View>
                    ))}
                  </View>

                  {/* Add new time button */}
                  <Button
                    mode="outlined"
                    onPress={() => {
                      const newTime = '09:00'; // Default time
                      const newTimes = [...(formData.reminderTimes || []), newTime];
                      setFormData({ ...formData, reminderTimes: newTimes });
                    }}
                    style={styles.addTimeButton}
                    icon="plus"
                  >
                    Add Time
                  </Button>
                </View>
              )}

              {!formData.reminderInterval && (
                <View style={styles.specificTimesContainer}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, marginBottom: 8 }}>
                    Specific reminder times
                  </Text>

                  {/* Display current specific times */}
                  <View style={styles.specificTimesList}>
                    {(formData.reminderTimes || []).map((time, index) => (
                      <View key={index} style={styles.specificTimeItem}>
                        <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                          {time}
                        </Text>
                        <Button
                          mode="text"
                          compact
                          onPress={() => {
                            const newTimes = [...(formData.reminderTimes || [])];
                            newTimes.splice(index, 1);
                            setFormData({ ...formData, reminderTimes: newTimes });
                          }}
                        >
                          Remove
                        </Button>
                      </View>
                    ))}
                  </View>

                  {/* Add new time button */}
                  <Button
                    mode="outlined"
                    onPress={() => {
                      const newTime = '09:00'; // Default time
                      const newTimes = [...(formData.reminderTimes || []), newTime];
                      setFormData({ ...formData, reminderTimes: newTimes });
                    }}
                    style={styles.addTimeButton}
                    icon="plus"
                  >
                    Add Time
                  </Button>
                </View>
              )}
            </View>
          )}
        </View>

        <Divider />

        {/* Reminder Settings */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Reminders
          </Text>
          
          <List.Item
            title="Enable Reminders"
            description="Get daily notifications"
            left={() => <MaterialCommunityIcons name="bell" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <Switch
                value={formData.reminderEnabled}
                onValueChange={(value) => setFormData({ ...formData, reminderEnabled: value })}
              />
            )}
          />

          {formData.reminderEnabled && (
            <>
              {Platform.OS === 'web' ? (
                <View style={styles.webTimePickerContainer}>
                  <Text variant="titleSmall" style={[styles.webTimePickerLabel, { color: theme.colors.onSurface }]}>
                    Reminder Time
                  </Text>
                  <TextInput
                    mode="outlined"
                    value={formData.reminderTime}
                    onChangeText={handleWebTimeChange}
                    placeholder="09:00"
                    style={styles.webTimeInput}
                    left={<TextInput.Icon icon="clock" />}
                  />
                </View>
              ) : (
                <List.Item
                  title="Reminder Time"
                  description={formData.reminderTime}
                  left={() => <MaterialCommunityIcons name="clock" size={24} color={theme.colors.onSurface} />}
                  onPress={handleTimePickerOpen}
                />
              )}
            </>
          )}
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={[styles.actionButtons, { backgroundColor: theme.colors.surface }]}>
        <Button
          mode="outlined"
          onPress={handleCancel}
          style={styles.button}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.button}
          loading={loading}
          disabled={loading}
        >
          Create Habit
        </Button>
      </View>

      {/* Time Picker */}
      {Platform.OS === 'ios' && showTimePicker && (
        <Portal>
          <Modal
            visible={showTimePicker}
            onDismiss={handleTimeCancel}
            contentContainerStyle={[styles.timePickerModal, { backgroundColor: theme.colors.surface }]}
          >
            <Text variant="titleMedium" style={[styles.modalTitle, { color: theme.colors.onSurface }]}>
              Select Reminder Time
            </Text>
            <DateTimePicker
              value={tempTime}
              mode="time"
              display="spinner"
              onChange={handleTimeChange}
              style={styles.timePicker}
            />
            <View style={styles.modalButtons}>
              <Button mode="outlined" onPress={handleTimeCancel} style={styles.modalButton}>
                Cancel
              </Button>
              <Button mode="contained" onPress={handleTimeConfirm} style={styles.modalButton}>
                Confirm
              </Button>
            </View>
          </Modal>
        </Portal>
      )}

      {Platform.OS === 'android' && showTimePicker && (
        <DateTimePicker
          value={tempTime}
          mode="time"
          display="default"
          onChange={handleTimeChange}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100, // Space for action buttons
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 12,
    fontWeight: '600',
  },
  textInput: {
    marginBottom: 4,
  },
  errorText: {
    marginTop: 4,
    marginLeft: 12,
  },
  emojiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  emojiChip: {
    marginBottom: 8,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  colorChip: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginBottom: 8,
    borderWidth: 2,
  },
  customDaysContainer: {
    marginTop: 12,
    paddingHorizontal: 16,
  },
  daysGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    justifyContent: 'center',
  },
  dayChip: {
    minWidth: 45,
  },
  actionButtons: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  button: {
    flex: 1,
  },
  timePickerModal: {
    margin: 20,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  modalTitle: {
    marginBottom: 20,
    textAlign: 'center',
  },
  timePicker: {
    width: '100%',
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
  },
  modalButton: {
    flex: 1,
  },
  multipleDailyContainer: {
    marginTop: 12,
    paddingHorizontal: 16,
  },
  subsectionTitle: {
    marginBottom: 8,
    fontWeight: '600',
  },
  targetSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  targetButton: {
    minWidth: 48,
    marginHorizontal: 16,
  },
  targetText: {
    minWidth: 60,
    textAlign: 'center',
    fontWeight: '700',
  },
  targetDescription: {
    textAlign: 'center',
    marginBottom: 16,
  },
  reminderStyleContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 12,
  },
  reminderStyleChip: {
    flex: 1,
  },
  intervalContainer: {
    marginTop: 8,
  },
  intervalSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  intervalChip: {
    minWidth: 50,
  },
  webTimePickerContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  webTimePickerLabel: {
    marginBottom: 8,
    fontWeight: '600',
  },
  webTimeInput: {
    marginBottom: 8,
  },
  specificTimesContainer: {
    marginTop: 8,
  },
  specificTimesList: {
    marginBottom: 12,
  },
  specificTimeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
    marginBottom: 8,
  },
  addTimeButton: {
    marginTop: 8,
  },
  timeEditButton: {
    flex: 1,
    justifyContent: 'flex-start',
  },
});
