import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, Platform } from 'react-native';
import {
  Text,
  TextInput,
  Button,
  useTheme,
  Chip,
  Switch,
  List,
  Divider,
  Portal,
  Modal,
  IconButton,
} from 'react-native-paper';
import DateTimePicker from '@react-native-community/datetimepicker';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { HabitFormData, HABIT_COLORS, HabitFrequency } from '../../src/types/habit';
import { habitService } from '../../src/services/habitService';
import { SuccessModal } from '../../src/components/ui/SuccessModal';

const EMOJI_OPTIONS = [
  '💪', '📚', '🏃', '💧', '🧘', '🎯', '🌱', '✍️', '🎵', '🍎',
  '🏋️', '🚴', '🏊', '🧠', '💤', '🍽️', '🚫', '📱', '💊', '🧹',
  '🎨', '📝', '🔥', '⚡', '🌟', '🎉', '🏆', '💎', '🌈', '🚀'
];
const DAYS_OF_WEEK = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export default function CreateHabitScreen() {
  const theme = useTheme();
  const [formData, setFormData] = useState<HabitFormData>({
    title: '',
    emoji: '',
    icons: [],
    color: HABIT_COLORS[Math.floor(Math.random() * HABIT_COLORS.length)], // Random initial color
    frequency: 'daily',
    customDays: [],
    reminderTime: '09:00',
    reminderEnabled: false,
    dailyTarget: 1,
    reminderInterval: 60,
    reminderTimes: [],
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [tempTime, setTempTime] = useState(new Date());
  const [editingTimeIndex, setEditingTimeIndex] = useState<number | null>(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [multiIconMode, setMultiIconMode] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Habit title is required';
    }

    if (formData.frequency === 'custom' && (!formData.customDays || formData.customDays.length === 0)) {
      newErrors.customDays = 'Please select at least one day';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    console.log('handleSave called with formData:', formData);

    if (!validateForm()) {
      console.log('Form validation failed');
      return;
    }

    setLoading(true);
    try {
      console.log('Creating habit with data:', formData);
      const newHabit = await habitService.createHabit(formData);
      console.log('Habit created successfully:', newHabit);

      setShowSuccessModal(true);
    } catch (error) {
      console.error('Failed to create habit:', error);
      Alert.alert('Error', `Failed to create habit: ${error.message || error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const toggleCustomDay = (dayIndex: number) => {
    const currentDays = formData.customDays || [];
    const newDays = currentDays.includes(dayIndex)
      ? currentDays.filter(d => d !== dayIndex)
      : [...currentDays, dayIndex].sort();

    setFormData({ ...formData, customDays: newDays });
  };

  const handleTimePickerOpen = () => {
    // Parse current time or use default
    const timeString = formData.reminderTime || '09:00';
    const [hours, minutes] = timeString.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    setTempTime(date);
    setShowTimePicker(true);
  };

  const handleTimeChange = (event: any, selectedTime?: Date) => {
    if (Platform.OS === 'android') {
      setShowTimePicker(false);
    }

    if (selectedTime) {
      setTempTime(selectedTime);
      if (Platform.OS === 'android') {
        const timeString = selectedTime.toTimeString().slice(0, 5);
        setFormData({ ...formData, reminderTime: timeString });
      }
    }
  };

  const handleTimeConfirm = () => {
    const timeString = tempTime.toTimeString().slice(0, 5);

    if (editingTimeIndex !== null) {
      if (editingTimeIndex === -1) {
        // Adding a new time
        const newTimes = [...(formData.reminderTimes || []), timeString];
        setFormData({ ...formData, reminderTimes: newTimes.sort() });
      } else {
        // Editing an existing time
        const newTimes = [...(formData.reminderTimes || [])];
        newTimes[editingTimeIndex] = timeString;
        setFormData({ ...formData, reminderTimes: newTimes.sort() });
      }
      setEditingTimeIndex(null);
    } else {
      // Regular reminder time
      setFormData({ ...formData, reminderTime: timeString });
    }

    setShowTimePicker(false);
  };

  const handleTimeCancel = () => {
    setShowTimePicker(false);
  };

  const handleWebTimeChange = (timeString: string) => {
    setFormData({ ...formData, reminderTime: timeString });
  };

  const handleRandomColor = () => {
    const availableColors = HABIT_COLORS.filter(color => color !== formData.color);
    const randomColor = availableColors[Math.floor(Math.random() * availableColors.length)];
    setFormData({ ...formData, color: randomColor });
  };

  const handleIconSelection = (emoji: string) => {
    if (multiIconMode) {
      const currentIcons = formData.icons || [];
      if (currentIcons.includes(emoji)) {
        // Remove icon
        setFormData({
          ...formData,
          icons: currentIcons.filter(icon => icon !== emoji)
        });
      } else if (currentIcons.length < 5) {
        // Add icon (max 5)
        setFormData({
          ...formData,
          icons: [...currentIcons, emoji]
        });
      }
    } else {
      // Single icon mode - allow deselection
      if (formData.emoji === emoji) {
        // Deselect current icon
        setFormData({ ...formData, emoji: '' });
      } else {
        // Select new icon
        setFormData({ ...formData, emoji });
      }
    }
  };

  const isIconSelected = (emoji: string) => {
    if (multiIconMode) {
      return (formData.icons || []).includes(emoji);
    } else {
      return formData.emoji === emoji;
    }
  };

  const handleEditSpecificTime = (index: number, currentTime: string) => {
    const [hours, minutes] = currentTime.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    setTempTime(date);
    setEditingTimeIndex(index);
    setShowTimePicker(true);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Title Input */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface, fontWeight: '700' }]}>
            📝 Habit Details
          </Text>
          <TextInput
            label="Habit Title"
            value={formData.title}
            onChangeText={(text) => setFormData({ ...formData, title: text })}
            mode="outlined"
            error={!!errors.title}
            style={styles.textInput}
          />
          {errors.title && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
              {errors.title}
            </Text>
          )}
        </View>

        {/* Emoji Selection */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface, fontWeight: '700' }]}>
              🎨 Choose Icons
            </Text>
            <View style={styles.multiIconToggle}>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginRight: 8 }}>
                Multiple Icons
              </Text>
              <Switch
                value={multiIconMode}
                onValueChange={(value) => {
                  setMultiIconMode(value);
                  if (!value) {
                    // Reset to single icon mode
                    setFormData({
                      ...formData,
                      emoji: (formData.icons && formData.icons[0]) || '',
                      icons: []
                    });
                  } else {
                    // Switch to multi-icon mode
                    setFormData({
                      ...formData,
                      icons: formData.emoji ? [formData.emoji] : [],
                      emoji: ''
                    });
                  }
                }}
              />
            </View>
          </View>

          {/* Selected Icons Display */}
          {multiIconMode ? (
            (formData.icons || []).length > 0 ? (
              <View style={styles.selectedIconsContainer}>
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginBottom: 8 }}>
                  Selected Icons ({(formData.icons || []).length}/5):
                </Text>
                <View style={styles.selectedIconsList}>
                  {(formData.icons || []).map((icon, index) => (
                    <Chip
                      key={index}
                      mode="flat"
                      onPress={() => {
                        const newIcons = [...(formData.icons || [])];
                        newIcons.splice(index, 1);
                        setFormData({ ...formData, icons: newIcons });
                      }}
                      style={[styles.selectedIconChip, { backgroundColor: theme.colors.primaryContainer }]}
                      textStyle={{ fontSize: 24 }}
                      icon="close"
                    >
                      {icon}
                    </Chip>
                  ))}
                </View>
              </View>
            ) : (
              <View style={styles.noIconsContainer}>
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', fontStyle: 'italic' }}>
                  No icons selected. Tap icons below to add up to 5.
                </Text>
              </View>
            )
          ) : (
            !formData.emoji && (
              <View style={styles.noIconContainer}>
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', fontStyle: 'italic' }}>
                  No icon selected. Tap an icon below to select, or tap again to deselect.
                </Text>
              </View>
            )
          )}

          <View style={styles.emojiGrid}>
            {EMOJI_OPTIONS.map((emoji) => (
              <Chip
                key={emoji}
                mode={isIconSelected(emoji) ? 'flat' : 'outlined'}
                selected={isIconSelected(emoji)}
                onPress={() => handleIconSelection(emoji)}
                style={[
                  styles.emojiChip,
                  multiIconMode && (formData.icons || []).length >= 5 && !isIconSelected(emoji) && styles.disabledChip
                ]}
                textStyle={{ fontSize: 28, lineHeight: 32 }}
                disabled={multiIconMode && (formData.icons || []).length >= 5 && !isIconSelected(emoji)}
              >
                {emoji}
              </Chip>
            ))}
          </View>
        </View>

        {/* Color Selection */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface, fontWeight: '700' }]}>
              🎨 Choose Color
            </Text>
            <Button
              mode="outlined"
              onPress={handleRandomColor}
              style={styles.randomColorButton}
              icon="dice-6"
              compact
            >
              Random
            </Button>
          </View>

          {/* Color Preview */}
          <View style={styles.colorPreview}>
            <View style={[styles.colorPreviewCircle, { backgroundColor: formData.color }]} />
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, marginLeft: 12 }}>
              Selected Color
            </Text>
          </View>

          {/* Color Picker Toggle */}
          <View style={styles.colorToggleContainer}>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginRight: 8 }}>
              Choose Specific Color
            </Text>
            <Switch
              value={showColorPicker}
              onValueChange={setShowColorPicker}
            />
          </View>

          {showColorPicker && (
            <View style={styles.colorGrid}>
              {HABIT_COLORS.map((color) => (
                <Chip
                  key={color}
                  mode={formData.color === color ? 'flat' : 'outlined'}
                  selected={formData.color === color}
                  onPress={() => setFormData({ ...formData, color })}
                  style={[
                    styles.colorChip,
                    {
                      backgroundColor: formData.color === color ? color : 'transparent',
                      borderColor: color,
                    }
                  ]}
                  textStyle={{
                    color: formData.color === color ? '#FFFFFF' : color
                  }}
                >
                  {formData.color === color ? '✓' : ' '}
                </Chip>
              ))}
            </View>
          )}
        </View>

        {/* Frequency Selection */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface, fontWeight: '700' }]}>
            📅 Frequency
          </Text>
          
          <List.Item
            title="Daily"
            description="Every day"
            left={() => <MaterialCommunityIcons name="calendar-today" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <MaterialCommunityIcons 
                name={formData.frequency === 'daily' ? 'radiobox-marked' : 'radiobox-blank'} 
                size={24} 
                color={theme.colors.primary} 
              />
            )}
            onPress={() => setFormData({ ...formData, frequency: 'daily' })}
          />
          
          <List.Item
            title="Weekly"
            description="Once a week"
            left={() => <MaterialCommunityIcons name="calendar-week" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <MaterialCommunityIcons 
                name={formData.frequency === 'weekly' ? 'radiobox-marked' : 'radiobox-blank'} 
                size={24} 
                color={theme.colors.primary} 
              />
            )}
            onPress={() => setFormData({ ...formData, frequency: 'weekly' })}
          />
          
          <List.Item
            title="Custom Days"
            description="Choose specific days"
            left={() => <MaterialCommunityIcons name="calendar-multiple" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <MaterialCommunityIcons
                name={formData.frequency === 'custom' ? 'radiobox-marked' : 'radiobox-blank'}
                size={24}
                color={theme.colors.primary}
              />
            )}
            onPress={() => setFormData({ ...formData, frequency: 'custom' })}
          />

          <List.Item
            title="Multiple Times Daily"
            description="Set a daily target (e.g., drink water 8 times)"
            left={() => <MaterialCommunityIcons name="repeat" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <MaterialCommunityIcons
                name={formData.frequency === 'multiple_daily' ? 'radiobox-marked' : 'radiobox-blank'}
                size={24}
                color={theme.colors.primary}
              />
            )}
            onPress={() => setFormData({ ...formData, frequency: 'multiple_daily' })}
          />

          {formData.frequency === 'custom' && (
            <View style={styles.customDaysContainer}>
              <View style={styles.daysGrid}>
                {DAYS_OF_WEEK.map((day, index) => (
                  <Chip
                    key={day}
                    mode={formData.customDays?.includes(index) ? 'flat' : 'outlined'}
                    selected={formData.customDays?.includes(index)}
                    onPress={() => toggleCustomDay(index)}
                    style={styles.dayChip}
                    compact
                  >
                    {day}
                  </Chip>
                ))}
              </View>
              {errors.customDays && (
                <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
                  {errors.customDays}
                </Text>
              )}
            </View>
          )}

          {formData.frequency === 'multiple_daily' && (
            <View style={styles.multipleDailyContainer}>
              {/* Only show daily target when using interval mode */}
              {formData.reminderInterval && (
                <>
                  <Text variant="titleSmall" style={[styles.subsectionTitle, { color: theme.colors.onSurface }]}>
                    Daily Target
                  </Text>
                  <View style={styles.targetSelector}>
                    <Button
                      mode="outlined"
                      onPress={() => setFormData({ ...formData, dailyTarget: Math.max(1, (formData.dailyTarget || 1) - 1) })}
                      style={styles.targetButton}
                      disabled={(formData.dailyTarget || 1) <= 1}
                    >
                      -
                    </Button>
                    <Text variant="headlineSmall" style={[styles.targetText, { color: theme.colors.primary }]}>
                      {formData.dailyTarget || 1}
                    </Text>
                    <Button
                      mode="outlined"
                      onPress={() => setFormData({ ...formData, dailyTarget: Math.min(20, (formData.dailyTarget || 1) + 1) })}
                      style={styles.targetButton}
                      disabled={(formData.dailyTarget || 1) >= 20}
                    >
                      +
                    </Button>
                  </View>
                  <Text variant="bodySmall" style={[styles.targetDescription, { color: theme.colors.onSurfaceVariant }]}>
                    Complete this habit {formData.dailyTarget || 1} time{(formData.dailyTarget || 1) > 1 ? 's' : ''} per day
                  </Text>
                </>
              )}

              <Text variant="titleSmall" style={[styles.subsectionTitle, { color: theme.colors.onSurface, marginTop: 16 }]}>
                Reminder Style
              </Text>
              <View style={styles.reminderStyleContainer}>
                <Chip
                  mode={!formData.reminderInterval ? 'flat' : 'outlined'}
                  selected={!formData.reminderInterval}
                  onPress={() => setFormData({ ...formData, reminderInterval: undefined, reminderTimes: [] })}
                  style={styles.reminderStyleChip}
                >
                  Specific Times
                </Chip>
                <Chip
                  mode={formData.reminderInterval ? 'flat' : 'outlined'}
                  selected={!!formData.reminderInterval}
                  onPress={() => setFormData({ ...formData, reminderInterval: 60, reminderTimes: [] })}
                  style={styles.reminderStyleChip}
                >
                  Interval
                </Chip>
              </View>

              {formData.reminderInterval && (
                <View style={styles.intervalContainer}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                    Remind every {formData.reminderInterval} minutes
                  </Text>
                  <View style={styles.intervalSelector}>
                    {[15, 30, 60, 120, 180].map((interval) => (
                      <Chip
                        key={interval}
                        mode={formData.reminderInterval === interval ? 'flat' : 'outlined'}
                        selected={formData.reminderInterval === interval}
                        onPress={() => setFormData({ ...formData, reminderInterval: interval })}
                        style={styles.intervalChip}
                        compact
                      >
                        {interval < 60 ? `${interval}m` : `${interval / 60}h`}
                      </Chip>
                    ))}
                  </View>
                </View>
              )}

              {!formData.reminderInterval && (
                <View style={styles.specificTimesContainer}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, marginBottom: 8 }}>
                    Specific reminder times
                  </Text>

                  {/* Display current specific times */}
                  <View style={styles.specificTimesList}>
                    {(formData.reminderTimes || []).map((time, index) => (
                      <View key={index} style={styles.specificTimeItem}>
                        <Button
                          mode="text"
                          onPress={() => handleEditSpecificTime(index, time)}
                          style={styles.timeEditButton}
                        >
                          <Text variant="bodyMedium" style={{ color: theme.colors.primary }}>
                            {time}
                          </Text>
                        </Button>
                        <Button
                          mode="text"
                          compact
                          onPress={() => {
                            const newTimes = [...(formData.reminderTimes || [])];
                            newTimes.splice(index, 1);
                            setFormData({ ...formData, reminderTimes: newTimes });
                          }}
                        >
                          Remove
                        </Button>
                      </View>
                    ))}
                  </View>

                  {/* Add new time button */}
                  {(formData.reminderTimes || []).length < 8 && (
                    <Button
                      mode="outlined"
                      onPress={() => {
                        setEditingTimeIndex(-1); // -1 indicates adding new time
                        setShowTimePicker(true);
                      }}
                      style={styles.addTimeButton}
                      icon="plus"
                    >
                      Add Reminder Time
                    </Button>
                  )}

                  {(formData.reminderTimes || []).length >= 8 && (
                    <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', marginTop: 8 }}>
                      Maximum 8 reminder times allowed
                    </Text>
                  )}
                </View>
              )}


            </View>
          )}
        </View>

        <Divider />

        {/* Reminder Settings */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface, fontWeight: '700' }]}>
            🔔 Reminders
          </Text>
          
          <List.Item
            title="Enable Reminders"
            description="Get daily notifications"
            left={() => <MaterialCommunityIcons name="bell" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <Switch
                value={formData.reminderEnabled}
                onValueChange={(value) => setFormData({ ...formData, reminderEnabled: value })}
              />
            )}
          />

          {formData.reminderEnabled && (
            <>
              {Platform.OS === 'web' ? (
                <View style={styles.webTimePickerContainer}>
                  <Text variant="titleSmall" style={[styles.webTimePickerLabel, { color: theme.colors.onSurface }]}>
                    Reminder Time
                  </Text>
                  <TextInput
                    mode="outlined"
                    value={formData.reminderTime}
                    onChangeText={handleWebTimeChange}
                    placeholder="09:00"
                    style={styles.webTimeInput}
                    left={<TextInput.Icon icon="clock" />}
                  />
                </View>
              ) : (
                <List.Item
                  title="Reminder Time"
                  description={formData.reminderTime}
                  left={() => <MaterialCommunityIcons name="clock" size={24} color={theme.colors.onSurface} />}
                  onPress={handleTimePickerOpen}
                />
              )}
            </>
          )}
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={[styles.actionButtons, { backgroundColor: theme.colors.surface }]}>
        <Button
          mode="outlined"
          onPress={handleCancel}
          style={styles.button}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.button}
          loading={loading}
          disabled={loading}
        >
          Create Habit
        </Button>
      </View>

      {/* Time Picker */}
      {Platform.OS === 'ios' && showTimePicker && (
        <Portal>
          <Modal
            visible={showTimePicker}
            onDismiss={handleTimeCancel}
            contentContainerStyle={[styles.timePickerModal, { backgroundColor: theme.colors.surface }]}
          >
            <Text variant="titleMedium" style={[styles.modalTitle, { color: theme.colors.onSurface }]}>
              Select Reminder Time
            </Text>
            <DateTimePicker
              value={tempTime}
              mode="time"
              display="spinner"
              onChange={handleTimeChange}
              style={styles.timePicker}
            />
            <View style={styles.modalButtons}>
              <Button mode="outlined" onPress={handleTimeCancel} style={styles.modalButton}>
                Cancel
              </Button>
              <Button mode="contained" onPress={handleTimeConfirm} style={styles.modalButton}>
                Confirm
              </Button>
            </View>
          </Modal>
        </Portal>
      )}

      {Platform.OS === 'android' && showTimePicker && (
        <DateTimePicker
          value={tempTime}
          mode="time"
          display="default"
          onChange={handleTimeChange}
        />
      )}

      {/* Success Modal */}
      <SuccessModal
        visible={showSuccessModal}
        onDismiss={() => setShowSuccessModal(false)}
        title="🎉 Habit Created Successfully!"
        message={`Your habit "${formData.title}" has been created and is ready to help you build better routines!`}
        icon="check-circle"
        primaryAction={{
          label: "Go to Home",
          onPress: () => {
            setShowSuccessModal(false);
            router.push('/(tabs)/');
          }
        }}
        secondaryAction={{
          label: "Create Another",
          onPress: () => {
            setShowSuccessModal(false);
            // Reset form for creating another habit
            setFormData({
              title: '',
              emoji: '',
              color: HABIT_COLORS[0],
              frequency: 'daily',
              customDays: [],
              reminderTime: '09:00',
              reminderEnabled: false,
              dailyTarget: 1,
              reminderInterval: 60,
              reminderTimes: [],
            });
          }
        }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100, // Space for action buttons
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 12,
    fontWeight: '600',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  multiIconToggle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedIconsContainer: {
    marginBottom: 16,
  },
  selectedIconsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  selectedIconChip: {
    marginBottom: 8,
  },
  disabledChip: {
    opacity: 0.5,
  },
  noIconsContainer: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
    borderStyle: 'dashed',
    borderWidth: 1,
    borderColor: '#ccc',
  },
  noIconContainer: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
    borderStyle: 'dashed',
    borderWidth: 1,
    borderColor: '#ccc',
  },
  colorActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  randomColorButton: {
    height: 36,
  },
  colorPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    padding: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
  },
  colorPreviewCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#ddd',
  },
  colorToggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  textInput: {
    marginBottom: 4,
  },
  errorText: {
    marginTop: 4,
    marginLeft: 12,
  },
  emojiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  emojiChip: {
    marginBottom: 12,
    minWidth: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  colorChip: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginBottom: 8,
    borderWidth: 2,
  },
  customDaysContainer: {
    marginTop: 12,
    paddingHorizontal: 16,
  },
  daysGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    justifyContent: 'center',
  },
  dayChip: {
    minWidth: 45,
  },
  actionButtons: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  button: {
    flex: 1,
  },
  timePickerModal: {
    margin: 20,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  modalTitle: {
    marginBottom: 20,
    textAlign: 'center',
  },
  timePicker: {
    width: '100%',
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
  },
  modalButton: {
    flex: 1,
  },
  multipleDailyContainer: {
    marginTop: 12,
    paddingHorizontal: 16,
  },
  subsectionTitle: {
    marginBottom: 8,
    fontWeight: '600',
  },
  targetSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  targetButton: {
    minWidth: 48,
    marginHorizontal: 16,
  },
  targetText: {
    minWidth: 60,
    textAlign: 'center',
    fontWeight: '700',
  },
  targetDescription: {
    textAlign: 'center',
    marginBottom: 16,
  },
  reminderStyleContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 12,
  },
  reminderStyleChip: {
    flex: 1,
  },
  intervalContainer: {
    marginTop: 8,
  },
  intervalSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  intervalChip: {
    minWidth: 50,
  },
  webTimePickerContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  webTimePickerLabel: {
    marginBottom: 8,
    fontWeight: '600',
  },
  webTimeInput: {
    marginBottom: 8,
  },
  specificTimesContainer: {
    marginTop: 8,
  },
  specificTimesList: {
    marginBottom: 12,
  },
  specificTimeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  timeChip: {
    marginRight: 8,
  },
  addTimeButton: {
    marginTop: 8,
  },
  timeEditButton: {
    flex: 1,
    justifyContent: 'flex-start',
  },
});
