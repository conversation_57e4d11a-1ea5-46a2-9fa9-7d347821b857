import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { 
  Text, 
  TextInput,
  Button,
  useTheme,
  Chip,
  Switch,
  List,
  Divider,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { HabitFormData, HABIT_COLORS, HabitFrequency } from '../../src/types/habit';
import { habitService } from '../../src/services/habitService';

const EMOJI_OPTIONS = ['💪', '📚', '🏃', '💧', '🧘', '🎯', '🌱', '✍️', '🎵', '🍎'];
const DAYS_OF_WEEK = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export default function CreateHabitScreen() {
  const theme = useTheme();
  const [formData, setFormData] = useState<HabitFormData>({
    title: '',
    emoji: '',
    color: HABIT_COLORS[0],
    frequency: 'daily',
    customDays: [],
    reminderTime: '09:00',
    reminderEnabled: false,
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Habit title is required';
    }

    if (formData.frequency === 'custom' && (!formData.customDays || formData.customDays.length === 0)) {
      newErrors.customDays = 'Please select at least one day';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await habitService.createHabit(formData);
      router.back();
    } catch (error) {
      console.error('Failed to create habit:', error);
      Alert.alert('Error', 'Failed to create habit. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const toggleCustomDay = (dayIndex: number) => {
    const currentDays = formData.customDays || [];
    const newDays = currentDays.includes(dayIndex)
      ? currentDays.filter(d => d !== dayIndex)
      : [...currentDays, dayIndex].sort();
    
    setFormData({ ...formData, customDays: newDays });
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Title Input */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Habit Details
          </Text>
          <TextInput
            label="Habit Title"
            value={formData.title}
            onChangeText={(text) => setFormData({ ...formData, title: text })}
            mode="outlined"
            error={!!errors.title}
            style={styles.textInput}
          />
          {errors.title && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
              {errors.title}
            </Text>
          )}
        </View>

        {/* Emoji Selection */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Choose an Icon
          </Text>
          <View style={styles.emojiGrid}>
            {EMOJI_OPTIONS.map((emoji) => (
              <Chip
                key={emoji}
                mode={formData.emoji === emoji ? 'flat' : 'outlined'}
                selected={formData.emoji === emoji}
                onPress={() => setFormData({ ...formData, emoji })}
                style={styles.emojiChip}
              >
                {emoji}
              </Chip>
            ))}
          </View>
        </View>

        {/* Color Selection */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Choose a Color
          </Text>
          <View style={styles.colorGrid}>
            {HABIT_COLORS.map((color) => (
              <Chip
                key={color}
                mode={formData.color === color ? 'flat' : 'outlined'}
                selected={formData.color === color}
                onPress={() => setFormData({ ...formData, color })}
                style={[
                  styles.colorChip,
                  { 
                    backgroundColor: formData.color === color ? color : 'transparent',
                    borderColor: color,
                  }
                ]}
                textStyle={{ 
                  color: formData.color === color ? '#FFFFFF' : color 
                }}
              >
                {formData.color === color ? '✓' : ' '}
              </Chip>
            ))}
          </View>
        </View>

        {/* Frequency Selection */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Frequency
          </Text>
          
          <List.Item
            title="Daily"
            description="Every day"
            left={() => <MaterialCommunityIcons name="calendar-today" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <MaterialCommunityIcons 
                name={formData.frequency === 'daily' ? 'radiobox-marked' : 'radiobox-blank'} 
                size={24} 
                color={theme.colors.primary} 
              />
            )}
            onPress={() => setFormData({ ...formData, frequency: 'daily' })}
          />
          
          <List.Item
            title="Weekly"
            description="Once a week"
            left={() => <MaterialCommunityIcons name="calendar-week" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <MaterialCommunityIcons 
                name={formData.frequency === 'weekly' ? 'radiobox-marked' : 'radiobox-blank'} 
                size={24} 
                color={theme.colors.primary} 
              />
            )}
            onPress={() => setFormData({ ...formData, frequency: 'weekly' })}
          />
          
          <List.Item
            title="Custom Days"
            description="Choose specific days"
            left={() => <MaterialCommunityIcons name="calendar-multiple" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <MaterialCommunityIcons 
                name={formData.frequency === 'custom' ? 'radiobox-marked' : 'radiobox-blank'} 
                size={24} 
                color={theme.colors.primary} 
              />
            )}
            onPress={() => setFormData({ ...formData, frequency: 'custom' })}
          />

          {formData.frequency === 'custom' && (
            <View style={styles.customDaysContainer}>
              <View style={styles.daysGrid}>
                {DAYS_OF_WEEK.map((day, index) => (
                  <Chip
                    key={day}
                    mode={formData.customDays?.includes(index) ? 'flat' : 'outlined'}
                    selected={formData.customDays?.includes(index)}
                    onPress={() => toggleCustomDay(index)}
                    style={styles.dayChip}
                    compact
                  >
                    {day}
                  </Chip>
                ))}
              </View>
              {errors.customDays && (
                <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
                  {errors.customDays}
                </Text>
              )}
            </View>
          )}
        </View>

        <Divider />

        {/* Reminder Settings */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Reminders
          </Text>
          
          <List.Item
            title="Enable Reminders"
            description="Get daily notifications"
            left={() => <MaterialCommunityIcons name="bell" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <Switch
                value={formData.reminderEnabled}
                onValueChange={(value) => setFormData({ ...formData, reminderEnabled: value })}
              />
            )}
          />

          {formData.reminderEnabled && (
            <List.Item
              title="Reminder Time"
              description={formData.reminderTime}
              left={() => <MaterialCommunityIcons name="clock" size={24} color={theme.colors.onSurface} />}
              onPress={() => {
                // TODO: Open time picker
                Alert.alert('Coming Soon', 'Time picker will be available soon!');
              }}
            />
          )}
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={[styles.actionButtons, { backgroundColor: theme.colors.surface }]}>
        <Button
          mode="outlined"
          onPress={handleCancel}
          style={styles.button}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.button}
          loading={loading}
          disabled={loading}
        >
          Create Habit
        </Button>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100, // Space for action buttons
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 12,
    fontWeight: '600',
  },
  textInput: {
    marginBottom: 4,
  },
  errorText: {
    marginTop: 4,
    marginLeft: 12,
  },
  emojiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  emojiChip: {
    marginBottom: 8,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  colorChip: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginBottom: 8,
    borderWidth: 2,
  },
  customDaysContainer: {
    marginTop: 12,
    paddingHorizontal: 16,
  },
  daysGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    justifyContent: 'center',
  },
  dayChip: {
    minWidth: 45,
  },
  actionButtons: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  button: {
    flex: 1,
  },
});
