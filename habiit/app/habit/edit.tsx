import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { 
  Text, 
  TextInput,
  Button,
  useTheme,
  Chip,
  Switch,
  List,
  Divider,
  IconButton,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { HabitFormData, HABIT_COLORS, Habit } from '../../src/types/habit';
import { habitService } from '../../src/services/habitService';
import { SuccessModal } from '../../src/components/ui/SuccessModal';

const EMOJI_OPTIONS = [
  '💪', '📚', '🏃', '💧', '🧘', '🎯', '🌱', '✍️', '🎵', '🍎',
  '🏋️', '🚴', '🏊', '🧠', '💤', '🍽️', '🚫', '📱', '💊', '🧹',
  '🎨', '📝', '🔥', '⚡', '🌟', '🎉', '🏆', '💎', '🌈', '🚀'
];
const DAYS_OF_WEEK = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export default function EditHabitScreen() {
  const theme = useTheme();
  const { habitId } = useLocalSearchParams<{ habitId: string }>();
  const [habit, setHabit] = useState<Habit | null>(null);
  const [formData, setFormData] = useState<HabitFormData>({
    title: '',
    emoji: '',
    color: HABIT_COLORS[0],
    frequency: 'daily',
    customDays: [],
    reminderTime: '09:00',
    reminderEnabled: false,
  });
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  useEffect(() => {
    loadHabit();
  }, [habitId]);

  const loadHabit = async () => {
    if (!habitId) {
      Alert.alert('Error', 'Habit ID not provided');
      router.back();
      return;
    }

    try {
      const habitData = await habitService.getHabitById(habitId);
      if (!habitData) {
        Alert.alert('Error', 'Habit not found');
        router.back();
        return;
      }

      setHabit(habitData);
      setFormData({
        title: habitData.title,
        emoji: habitData.emoji || '',
        color: habitData.color,
        frequency: habitData.frequency,
        customDays: habitData.customDays || [],
        reminderTime: habitData.reminderTime || '09:00',
        reminderEnabled: habitData.reminderEnabled,
      });
    } catch (error) {
      console.error('Failed to load habit:', error);
      Alert.alert('Error', 'Failed to load habit');
      router.back();
    } finally {
      setInitialLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Habit title is required';
    }

    if (formData.frequency === 'custom' && (!formData.customDays || formData.customDays.length === 0)) {
      newErrors.customDays = 'Please select at least one day';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm() || !habitId) {
      return;
    }

    setLoading(true);
    try {
      await habitService.updateHabit(habitId, formData);
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Failed to update habit:', error);
      Alert.alert('Error', 'Failed to update habit. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Habit',
      'Are you sure you want to delete this habit? This action cannot be undone and will remove all completion data.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            if (!habitId) return;
            
            setLoading(true);
            try {
              await habitService.deleteHabit(habitId);
              router.back();
            } catch (error) {
              console.error('Failed to delete habit:', error);
              Alert.alert('Error', 'Failed to delete habit. Please try again.');
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  const handleCancel = () => {
    router.back();
  };

  const toggleCustomDay = (dayIndex: number) => {
    const currentDays = formData.customDays || [];
    const newDays = currentDays.includes(dayIndex)
      ? currentDays.filter(d => d !== dayIndex)
      : [...currentDays, dayIndex].sort();
    
    setFormData({ ...formData, customDays: newDays });
  };

  if (initialLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text variant="bodyLarge" style={{ color: theme.colors.onSurfaceVariant }}>
            Loading habit...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Header with Delete Button */}
        <View style={styles.header}>
          <Text variant="headlineSmall" style={[styles.headerTitle, { color: theme.colors.onSurface }]}>
            Edit Habit
          </Text>
          <IconButton
            icon="delete"
            iconColor={theme.colors.error}
            onPress={handleDelete}
            disabled={loading}
          />
        </View>

        {/* Title Input */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Habit Details
          </Text>
          <TextInput
            label="Habit Title"
            value={formData.title}
            onChangeText={(text) => setFormData({ ...formData, title: text })}
            mode="outlined"
            error={!!errors.title}
            style={styles.textInput}
          />
          {errors.title && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
              {errors.title}
            </Text>
          )}
        </View>

        {/* Emoji Selection */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Choose an Icon
          </Text>

          {/* No Icon Selected Indicator */}
          {!formData.emoji && (
            <View style={styles.noIconContainer}>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', fontStyle: 'italic' }}>
                No icon selected. Tap an icon below to select, or tap again to deselect.
              </Text>
            </View>
          )}

          <View style={styles.emojiGrid}>
            {EMOJI_OPTIONS.map((emoji) => (
              <Chip
                key={emoji}
                mode={formData.emoji === emoji ? 'flat' : 'outlined'}
                selected={formData.emoji === emoji}
                onPress={() => {
                  // Allow deselection in single icon mode
                  if (formData.emoji === emoji) {
                    setFormData({ ...formData, emoji: '' });
                  } else {
                    setFormData({ ...formData, emoji });
                  }
                }}
                style={styles.emojiChip}
                textStyle={{ fontSize: 28, lineHeight: 32 }}
              >
                {emoji}
              </Chip>
            ))}
          </View>
        </View>

        {/* Color Selection */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Choose a Color
          </Text>
          <View style={styles.colorGrid}>
            {HABIT_COLORS.map((color) => (
              <Chip
                key={color}
                mode={formData.color === color ? 'flat' : 'outlined'}
                selected={formData.color === color}
                onPress={() => setFormData({ ...formData, color })}
                style={[
                  styles.colorChip,
                  { 
                    backgroundColor: formData.color === color ? color : 'transparent',
                    borderColor: color,
                  }
                ]}
                textStyle={{ 
                  color: formData.color === color ? '#FFFFFF' : color 
                }}
              >
                {formData.color === color ? '✓' : ' '}
              </Chip>
            ))}
          </View>
        </View>

        {/* Frequency Selection */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Frequency
          </Text>
          
          <List.Item
            title="Daily"
            description="Every day"
            left={() => <MaterialCommunityIcons name="calendar-today" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <MaterialCommunityIcons 
                name={formData.frequency === 'daily' ? 'radiobox-marked' : 'radiobox-blank'} 
                size={24} 
                color={theme.colors.primary} 
              />
            )}
            onPress={() => setFormData({ ...formData, frequency: 'daily' })}
          />
          
          <List.Item
            title="Weekly"
            description="Once a week"
            left={() => <MaterialCommunityIcons name="calendar-week" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <MaterialCommunityIcons 
                name={formData.frequency === 'weekly' ? 'radiobox-marked' : 'radiobox-blank'} 
                size={24} 
                color={theme.colors.primary} 
              />
            )}
            onPress={() => setFormData({ ...formData, frequency: 'weekly' })}
          />
          
          <List.Item
            title="Custom Days"
            description="Choose specific days"
            left={() => <MaterialCommunityIcons name="calendar-multiple" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <MaterialCommunityIcons 
                name={formData.frequency === 'custom' ? 'radiobox-marked' : 'radiobox-blank'} 
                size={24} 
                color={theme.colors.primary} 
              />
            )}
            onPress={() => setFormData({ ...formData, frequency: 'custom' })}
          />

          {formData.frequency === 'custom' && (
            <View style={styles.customDaysContainer}>
              <View style={styles.daysGrid}>
                {DAYS_OF_WEEK.map((day, index) => (
                  <Chip
                    key={day}
                    mode={formData.customDays?.includes(index) ? 'flat' : 'outlined'}
                    selected={formData.customDays?.includes(index)}
                    onPress={() => toggleCustomDay(index)}
                    style={styles.dayChip}
                    compact
                  >
                    {day}
                  </Chip>
                ))}
              </View>
              {errors.customDays && (
                <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
                  {errors.customDays}
                </Text>
              )}
            </View>
          )}
        </View>

        <Divider />

        {/* Reminder Settings */}
        <View style={styles.section}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Reminders
          </Text>
          
          <List.Item
            title="Enable Reminders"
            description="Get daily notifications"
            left={() => <MaterialCommunityIcons name="bell" size={24} color={theme.colors.onSurface} />}
            right={() => (
              <Switch
                value={formData.reminderEnabled}
                onValueChange={(value) => setFormData({ ...formData, reminderEnabled: value })}
              />
            )}
          />

          {formData.reminderEnabled && (
            <List.Item
              title="Reminder Time"
              description={formData.reminderTime}
              left={() => <MaterialCommunityIcons name="clock" size={24} color={theme.colors.onSurface} />}
              onPress={() => {
                // TODO: Open time picker
                Alert.alert('Coming Soon', 'Time picker will be available soon!');
              }}
            />
          )}
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={[styles.actionButtons, { backgroundColor: theme.colors.surface }]}>
        <Button
          mode="outlined"
          onPress={handleCancel}
          style={styles.button}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.button}
          loading={loading}
          disabled={loading}
        >
          Save Changes
        </Button>
      </View>

      {/* Success Modal */}
      <SuccessModal
        visible={showSuccessModal}
        onDismiss={() => setShowSuccessModal(false)}
        title="✨ Habit Updated Successfully!"
        message={`Your habit "${formData.title}" has been updated with your latest changes!`}
        icon="check-circle"
        primaryAction={{
          label: "Go to Home",
          onPress: () => {
            setShowSuccessModal(false);
            router.push('/(tabs)/');
          }
        }}
        secondaryAction={{
          label: "Continue Editing",
          onPress: () => {
            setShowSuccessModal(false);
          }
        }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100, // Space for action buttons
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  headerTitle: {
    fontWeight: '600',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 12,
    fontWeight: '600',
  },
  textInput: {
    marginBottom: 4,
  },
  errorText: {
    marginTop: 4,
    marginLeft: 12,
  },
  emojiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  emojiChip: {
    marginBottom: 12,
    minWidth: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noIconContainer: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
    borderStyle: 'dashed',
    borderWidth: 1,
    borderColor: '#ccc',
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  colorChip: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginBottom: 8,
    borderWidth: 2,
  },
  customDaysContainer: {
    marginTop: 12,
    paddingHorizontal: 16,
  },
  daysGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    justifyContent: 'center',
  },
  dayChip: {
    minWidth: 45,
  },
  actionButtons: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  button: {
    flex: 1,
  },
});
