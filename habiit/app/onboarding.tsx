import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Text,
  Button,
  useTheme,
  Card,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
} from 'react-native-reanimated';

import { storageService } from '../src/services/storage';
import { STORAGE_KEYS, AppSettings } from '../src/types/app';

interface OnboardingFeature {
  icon: string;
  title: string;
  description: string;
  color: string;
}

const features: OnboardingFeature[] = [
  {
    icon: 'shield-check',
    title: 'Privacy First',
    description: 'Your data stays on your device. No cloud sync, no tracking, no data collection.',
    color: '#10B981',
  },
  {
    icon: 'target',
    title: 'Build Better Habits',
    description: 'Track daily habits, build streaks, and achieve your goals with ease.',
    color: '#6366F1',
  },
  {
    icon: 'chart-line',
    title: 'Track Your Progress',
    description: 'Visualize your journey with detailed statistics and insights.',
    color: '#F59E0B',
  },
  {
    icon: 'bell',
    title: 'Stay Motivated',
    description: 'Get gentle reminders and celebrate milestones. All notifications are local.',
    color: '#EF4444',
  },
];

export default function OnboardingScreen() {
  const theme = useTheme();
  const fadeAnim = useSharedValue(0);
  const scaleAnim = useSharedValue(0.8);

  React.useEffect(() => {
    // Animate in on mount
    fadeAnim.value = withTiming(1, { duration: 800 });
    scaleAnim.value = withSpring(1, { damping: 15 });
  }, []);

  const handleGetStarted = async () => {
    try {
      // Mark onboarding as completed
      const settings: AppSettings = {
        theme: 'system',
        language: 'en',
        notificationsEnabled: true,
        defaultReminderTime: '09:00',
        isPremium: false,
        onboardingCompleted: true,
      };

      await storageService.setSecureItem(STORAGE_KEYS.SETTINGS, settings);

      // Navigate to main app
      router.replace('/(tabs)');
    } catch (error) {
      console.error('Failed to complete onboarding:', error);
      // Still navigate to main app even if saving fails
      router.replace('/(tabs)');
    }
  };

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: fadeAnim.value,
    transform: [{ scale: scaleAnim.value }],
  }));

  const renderFeature = (feature: OnboardingFeature, index: number) => (
    <Card key={index} style={[styles.featureCard, { backgroundColor: theme.colors.surface }]}>
      <Card.Content style={styles.featureContent}>
        <View style={[styles.featureIcon, { backgroundColor: `${feature.color}20` }]}>
          <MaterialCommunityIcons
            name={feature.icon as any}
            size={32}
            color={feature.color}
          />
        </View>
        <View style={styles.featureText}>
          <Text
            variant="titleMedium"
            style={[styles.featureTitle, { color: theme.colors.onSurface }]}
          >
            {feature.title}
          </Text>
          <Text
            variant="bodyMedium"
            style={[styles.featureDescription, { color: theme.colors.onSurfaceVariant }]}
          >
            {feature.description}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Animated.View style={[styles.content, animatedStyle]}>
        {/* Header */}
        <View style={styles.header}>
          <Text variant="displaySmall" style={[styles.appName, { color: theme.colors.primary }]}>
            Habiit
          </Text>
          <Text
            variant="headlineSmall"
            style={[styles.subtitle, { color: theme.colors.onSurface }]}
          >
            Privacy-First Habit Tracker
          </Text>
        </View>

        {/* Hero Section */}
        <View style={styles.heroSection}>
          <View style={[styles.heroIcon, { backgroundColor: `${theme.colors.primary}20` }]}>
            <MaterialCommunityIcons
              name="shield-heart"
              size={80}
              color={theme.colors.primary}
            />
          </View>
          <Text
            variant="headlineMedium"
            style={[styles.heroTitle, { color: theme.colors.onSurface }]}
          >
            Your Data, Your Device
          </Text>
          <Text
            variant="bodyLarge"
            style={[styles.heroDescription, { color: theme.colors.onSurfaceVariant }]}
          >
            Build better habits with complete privacy. All your data stays on your device - no cloud sync, no tracking, no data collection.
          </Text>
        </View>

        {/* Features */}
        <ScrollView style={styles.featuresContainer} showsVerticalScrollIndicator={false}>
          {features.map((feature, index) => renderFeature(feature, index))}
        </ScrollView>

        {/* Get Started Button */}
        <View style={styles.bottomActions}>
          <Button
            mode="contained"
            onPress={handleGetStarted}
            style={styles.getStartedButton}
            contentStyle={styles.buttonContent}
          >
            Get Started
          </Button>

          {/* Privacy Notice */}
          <View style={styles.privacyNotice}>
            <MaterialCommunityIcons
              name="shield-check"
              size={16}
              color={theme.colors.primary}
            />
            <Text
              variant="bodySmall"
              style={[styles.privacyText, { color: theme.colors.onSurfaceVariant }]}
            >
              100% private. Your data never leaves your device.
            </Text>
          </View>
        </View>
      </Animated.View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    paddingTop: 20,
    paddingBottom: 40,
  },
  appName: {
    fontWeight: '700',
    marginBottom: 8,
  },
  subtitle: {
    fontWeight: '500',
    textAlign: 'center',
  },
  heroSection: {
    alignItems: 'center',
    paddingBottom: 40,
  },
  heroIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  heroTitle: {
    textAlign: 'center',
    marginBottom: 16,
    fontWeight: '600',
  },
  heroDescription: {
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  featuresContainer: {
    flex: 1,
    marginBottom: 20,
  },
  featureCard: {
    marginBottom: 16,
    elevation: 1,
  },
  featureContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
  },
  featureIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureText: {
    flex: 1,
  },
  featureTitle: {
    fontWeight: '600',
    marginBottom: 4,
  },
  featureDescription: {
    lineHeight: 20,
  },
  bottomActions: {
    paddingBottom: 20,
  },
  getStartedButton: {
    marginBottom: 16,
  },
  buttonContent: {
    paddingVertical: 12,
  },
  privacyNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  privacyText: {
    marginLeft: 6,
    fontSize: 12,
  },
});
