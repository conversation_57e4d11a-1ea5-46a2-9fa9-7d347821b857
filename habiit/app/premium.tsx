import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { 
  Text, 
  Button,
  useTheme,
  Card,
  List,
  Chip,
  Divider,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { AppSettings, PurchaseProduct } from '../src/types/app';
import { storageService } from '../src/services/storage';
import { STORAGE_KEYS } from '../src/types/app';

// Mock premium features and pricing
const PREMIUM_FEATURES = [
  {
    icon: 'infinity',
    title: 'Unlimited Habits',
    description: 'Create as many habits as you want (free version limited to 5)',
    color: '#6366F1',
  },
  {
    icon: 'clock-time-four',
    title: 'Custom Reminder Times',
    description: 'Set individual reminder times for each habit',
    color: '#10B981',
  },
  {
    icon: 'download',
    title: 'Data Export',
    description: 'Export your habit data in CSV format for backup or analysis',
    color: '#F59E0B',
  },
  {
    icon: 'palette',
    title: 'Additional Themes',
    description: 'Access to premium color themes and customization options',
    color: '#EF4444',
  },
  {
    icon: 'chart-box',
    title: 'Advanced Statistics',
    description: 'Detailed analytics, trends, and insights about your habits',
    color: '#8B5CF6',
  },
  {
    icon: 'heart',
    title: 'Support Development',
    description: 'Help us continue improving Habiit and keep it privacy-focused',
    color: '#EC4899',
  },
];

const PRICING_OPTIONS: PurchaseProduct[] = [
  {
    productId: 'habiit_premium_monthly',
    title: 'Monthly Premium',
    description: 'All premium features',
    price: '$2.99/month',
    type: 'subscription',
  },
  {
    productId: 'habiit_premium_yearly',
    title: 'Yearly Premium',
    description: 'All premium features + 2 months free',
    price: '$29.99/year',
    type: 'subscription',
  },
  {
    productId: 'habiit_premium_lifetime',
    title: 'Lifetime Premium',
    description: 'One-time purchase, lifetime access',
    price: '$49.99',
    type: 'one-time',
  },
];

export default function PremiumScreen() {
  const theme = useTheme();
  const [settings, setSettings] = useState<AppSettings | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>('habiit_premium_yearly');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await storageService.getSecureItem<AppSettings>(STORAGE_KEYS.SETTINGS);
      setSettings(savedSettings);
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  };

  const handlePurchase = async (productId: string) => {
    setLoading(true);
    
    try {
      // In a real app, you would use expo-in-app-purchases here
      // For now, we'll simulate a successful purchase
      
      Alert.alert(
        'Purchase Simulation',
        'This is a demo app. In the real version, this would process your purchase through the App Store or Google Play.',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Simulate Success',
            onPress: async () => {
              // Update settings to mark as premium
              const updatedSettings: AppSettings = {
                ...settings,
                theme: settings?.theme || 'system',
                language: settings?.language || 'en',
                notificationsEnabled: settings?.notificationsEnabled ?? true,
                defaultReminderTime: settings?.defaultReminderTime || '09:00',
                onboardingCompleted: settings?.onboardingCompleted ?? false,
                isPremium: true,
              };
              
              await storageService.setSecureItem(STORAGE_KEYS.SETTINGS, updatedSettings);
              setSettings(updatedSettings);
              
              Alert.alert(
                'Welcome to Premium!',
                'Thank you for supporting Habiit! You now have access to all premium features.',
                [
                  {
                    text: 'OK',
                    onPress: () => router.back(),
                  },
                ]
              );
            },
          },
        ]
      );
    } catch (error) {
      console.error('Purchase failed:', error);
      Alert.alert('Purchase Failed', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleRestorePurchases = async () => {
    setLoading(true);
    
    try {
      // In a real app, you would restore purchases here
      Alert.alert(
        'Restore Purchases',
        'This feature would restore your previous purchases in the real app.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Restore failed:', error);
      Alert.alert('Restore Failed', 'Could not restore purchases. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (settings?.isPremium) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.premiumActiveContainer}>
          <MaterialCommunityIcons 
            name="crown" 
            size={80} 
            color={theme.colors.primary} 
          />
          <Text 
            variant="headlineMedium" 
            style={[styles.premiumActiveTitle, { color: theme.colors.onSurface }]}
          >
            Premium Active
          </Text>
          <Text 
            variant="bodyLarge" 
            style={[styles.premiumActiveDescription, { color: theme.colors.onSurfaceVariant }]}
          >
            Thank you for supporting Habiit! You have access to all premium features.
          </Text>
          
          <Button
            mode="outlined"
            onPress={() => router.back()}
            style={styles.backButton}
          >
            Back to Settings
          </Button>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <View style={styles.header}>
          <MaterialCommunityIcons 
            name="crown" 
            size={60} 
            color={theme.colors.primary} 
          />
          <Text 
            variant="headlineMedium" 
            style={[styles.headerTitle, { color: theme.colors.onSurface }]}
          >
            Habiit Premium
          </Text>
          <Text 
            variant="bodyLarge" 
            style={[styles.headerDescription, { color: theme.colors.onSurfaceVariant }]}
          >
            Unlock the full potential of your habit tracking
          </Text>
        </View>

        {/* Features */}
        <View style={styles.featuresSection}>
          <Text 
            variant="titleLarge" 
            style={[styles.sectionTitle, { color: theme.colors.onSurface }]}
          >
            Premium Features
          </Text>
          
          {PREMIUM_FEATURES.map((feature, index) => (
            <Card key={index} style={[styles.featureCard, { backgroundColor: theme.colors.surface }]}>
              <Card.Content style={styles.featureContent}>
                <View style={[styles.featureIcon, { backgroundColor: `${feature.color}20` }]}>
                  <MaterialCommunityIcons 
                    name={feature.icon as any} 
                    size={24} 
                    color={feature.color} 
                  />
                </View>
                <View style={styles.featureText}>
                  <Text 
                    variant="titleMedium" 
                    style={[styles.featureTitle, { color: theme.colors.onSurface }]}
                  >
                    {feature.title}
                  </Text>
                  <Text 
                    variant="bodyMedium" 
                    style={[styles.featureDescription, { color: theme.colors.onSurfaceVariant }]}
                  >
                    {feature.description}
                  </Text>
                </View>
              </Card.Content>
            </Card>
          ))}
        </View>

        <Divider style={styles.divider} />

        {/* Pricing */}
        <View style={styles.pricingSection}>
          <Text 
            variant="titleLarge" 
            style={[styles.sectionTitle, { color: theme.colors.onSurface }]}
          >
            Choose Your Plan
          </Text>
          
          {PRICING_OPTIONS.map((option) => (
            <Card 
              key={option.productId} 
              style={[
                styles.pricingCard, 
                { 
                  backgroundColor: theme.colors.surface,
                  borderColor: selectedPlan === option.productId ? theme.colors.primary : 'transparent',
                  borderWidth: selectedPlan === option.productId ? 2 : 0,
                }
              ]}
            >
              <Card.Content style={styles.pricingContent}>
                <View style={styles.pricingHeader}>
                  <View style={styles.pricingInfo}>
                    <Text 
                      variant="titleMedium" 
                      style={[styles.pricingTitle, { color: theme.colors.onSurface }]}
                    >
                      {option.title}
                    </Text>
                    <Text 
                      variant="bodyMedium" 
                      style={[styles.pricingDescription, { color: theme.colors.onSurfaceVariant }]}
                    >
                      {option.description}
                    </Text>
                  </View>
                  <View style={styles.pricingPrice}>
                    <Text 
                      variant="titleLarge" 
                      style={[styles.priceText, { color: theme.colors.primary }]}
                    >
                      {option.price}
                    </Text>
                    {option.productId === 'habiit_premium_yearly' && (
                      <Chip 
                        mode="flat" 
                        compact 
                        style={[styles.bestValueChip, { backgroundColor: theme.colors.primaryContainer }]}
                        textStyle={{ color: theme.colors.onPrimaryContainer, fontSize: 10 }}
                      >
                        Best Value
                      </Chip>
                    )}
                  </View>
                </View>
                
                <Button
                  mode={selectedPlan === option.productId ? "contained" : "outlined"}
                  onPress={() => {
                    setSelectedPlan(option.productId);
                    handlePurchase(option.productId);
                  }}
                  loading={loading && selectedPlan === option.productId}
                  disabled={loading}
                  style={styles.purchaseButton}
                >
                  {selectedPlan === option.productId ? 'Purchase' : 'Select'}
                </Button>
              </Card.Content>
            </Card>
          ))}
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Button
            mode="text"
            onPress={handleRestorePurchases}
            disabled={loading}
            textColor={theme.colors.onSurfaceVariant}
          >
            Restore Purchases
          </Button>
          
          <Text 
            variant="bodySmall" 
            style={[styles.footerText, { color: theme.colors.onSurfaceVariant }]}
          >
            Your privacy remains our priority. Premium features are processed locally on your device.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  headerTitle: {
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  headerDescription: {
    textAlign: 'center',
  },
  featuresSection: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 16,
  },
  featureCard: {
    marginBottom: 12,
    elevation: 1,
  },
  featureContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureText: {
    flex: 1,
  },
  featureTitle: {
    fontWeight: '600',
    marginBottom: 4,
  },
  featureDescription: {
    lineHeight: 20,
  },
  divider: {
    marginVertical: 24,
  },
  pricingSection: {
    marginBottom: 32,
  },
  pricingCard: {
    marginBottom: 16,
    elevation: 2,
  },
  pricingContent: {
    paddingVertical: 16,
  },
  pricingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  pricingInfo: {
    flex: 1,
  },
  pricingTitle: {
    fontWeight: '600',
    marginBottom: 4,
  },
  pricingDescription: {
    lineHeight: 20,
  },
  pricingPrice: {
    alignItems: 'flex-end',
  },
  priceText: {
    fontWeight: '700',
  },
  bestValueChip: {
    marginTop: 4,
  },
  purchaseButton: {
    marginTop: 8,
  },
  footer: {
    alignItems: 'center',
    paddingTop: 16,
  },
  footerText: {
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 18,
  },
  premiumActiveContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  premiumActiveTitle: {
    fontWeight: '600',
    marginTop: 24,
    marginBottom: 16,
  },
  premiumActiveDescription: {
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  backButton: {
    minWidth: 200,
  },
});
