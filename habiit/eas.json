{"cli": {"version": ">= 12.0.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "ios": {"resourceClass": "m-medium"}, "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}}, "preview": {"distribution": "internal", "ios": {"resourceClass": "m-medium", "simulator": true}, "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "production": {"ios": {"resourceClass": "m-medium", "autoIncrement": "buildNumber"}, "android": {"autoIncrement": "versionCode"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "your-app-store-connect-app-id", "appleTeamId": "your-apple-team-id"}, "android": {"serviceAccountKeyPath": "./path/to/service-account-key.json", "track": "production"}}}}