# Habiit - Privacy-First Habit Tracker

<div align="center">
  <h3>🔒 Build better habits without compromising your privacy</h3>

  [![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
  [![React Native](https://img.shields.io/badge/React%20Native-0.74-blue.svg)](https://reactnative.dev/)
  [![Expo](https://img.shields.io/badge/Expo-51-black.svg)](https://expo.dev/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5.3-blue.svg)](https://www.typescriptlang.org/)
</div>

## 🔒 Privacy First

Habiit is designed with privacy as the core principle. **All your data stays on your device** - no cloud sync, no tracking, no data collection. Your habits, progress, and personal information never leave your phone.

## ✨ Features

### Core Features
- ✅ **Create & Manage Habits** - Add daily, weekly, or custom frequency habits
- 📊 **Track Progress** - Mark habits as complete and build streaks
- 🔥 **Streak Tracking** - Monitor current and longest streaks for motivation
- 📈 **Visual Statistics** - Calendar heatmaps and progress charts
- 🔔 **Local Notifications** - Privacy-respecting reminders (all local)
- 🌙 **Dark Mode** - Beautiful light and dark themes
- 🌍 **Localization** - English and Spanish support

### Privacy Features
- 🔐 **Local Encryption** - All data encrypted on device using expo-secure-store
- 📱 **No Cloud Sync** - Data never leaves your device
- 🚫 **No Tracking** - Zero analytics, telemetry, or user tracking
- 🛡️ **No Accounts** - No sign-up, login, or personal information required

### Premium Features
- ♾️ **Unlimited Habits** - Create as many habits as you want (free: 5 limit)
- ⏰ **Custom Reminder Times** - Individual reminder times per habit
- 📤 **Data Export** - Export your data in CSV format
- 🎨 **Additional Themes** - Premium color themes and customization
- 📊 **Advanced Statistics** - Detailed analytics and insights

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio/Emulator (for Android development)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/habiit.git
   cd habiit
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Run on your preferred platform**
   ```bash
   # iOS
   npm run ios

   # Android
   npm run android

   # Web
   npm run web
   ```

## 🏗️ Architecture

### Project Structure

```
habiit/
├── app/                    # Expo Router pages
│   ├── (tabs)/            # Tab navigation screens
│   ├── habit/             # Habit management screens
│   ├── onboarding.tsx     # Onboarding flow
│   ├── premium.tsx        # Premium features screen
│   └── _layout.tsx        # Root layout
├── src/
│   ├── components/        # Reusable UI components
│   │   ├── habit/         # Habit-specific components
│   │   └── ui/            # Generic UI components
│   ├── services/          # Business logic services
│   │   ├── habitService.ts      # Habit CRUD operations
│   │   ├── storage.ts           # Encrypted local storage
│   │   └── notificationService.ts # Local notifications
│   ├── types/             # TypeScript type definitions
│   ├── utils/             # Utility functions
│   ├── locales/           # Translation files
│   └── __tests__/         # Unit tests
├── assets/                # Static assets
└── docs/                  # Documentation
```

### Key Technologies

- **React Native + Expo** - Cross-platform mobile development
- **Expo Router** - File-based navigation
- **React Native Paper** - Material Design components
- **expo-secure-store** - Encrypted local storage
- **expo-notifications** - Local push notifications
- **TypeScript** - Type safety and better DX
- **Jest + React Native Testing Library** - Unit testing

## 🔧 Development

### Available Scripts

```bash
# Development
npm start              # Start Expo development server
npm run ios           # Run on iOS simulator
npm run android       # Run on Android emulator
npm run web           # Run on web browser

# Testing
npm test              # Run unit tests
npm run test:watch    # Run tests in watch mode
npm run test:coverage # Run tests with coverage report

# Building
eas build --platform ios     # Build for iOS
eas build --platform android # Build for Android

# Linting
npm run lint          # Run ESLint
```

## 🧪 Testing

The app includes comprehensive unit tests for core functionality:

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Test Coverage

- ✅ Habit Service (CRUD operations, streak calculation)
- ✅ Storage Service (encryption, data persistence)
- ✅ Notification Service (local notifications)
- ✅ i18n Utilities (translations, localization)
- ✅ UI Components (habit cards, progress bars)

## 🌍 Localization

Habiit supports multiple languages with easy extensibility:

### Supported Languages
- 🇺🇸 English (en)
- 🇪🇸 Spanish (es)

### Adding New Languages

1. Create translation file: `src/locales/{language}/common.json`
2. Add language to `src/utils/i18n.ts`
3. Update `getSupportedLanguages()` function

## 📱 Building for Production

### EAS Build (Recommended)

```bash
# Build for iOS
eas build --platform ios

# Build for Android
eas build --platform android

# Build for both platforms
eas build --platform all
```

## 🔐 Privacy & Security

### Data Handling
- **Local Storage Only** - All data stored on device using expo-secure-store
- **Encryption** - Data encrypted at rest using device keychain/keystore
- **No Network Requests** - App functions completely offline
- **No Analytics** - Zero tracking or data collection

### Security Measures
- **Secure Storage** - Uses iOS Keychain and Android Keystore
- **Data Validation** - Input validation and sanitization
- **Error Handling** - Graceful error handling without data exposure

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass (`npm test`)
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Expo](https://expo.dev/) - Amazing React Native platform
- [React Native Paper](https://reactnativepaper.com/) - Beautiful Material Design components
- [React Native Community](https://reactnative.dev/community/overview) - Incredible ecosystem

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/yourusername/habiit/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/yourusername/habiit/discussions)

---

<div align="center">
  <p>Made with ❤️ for privacy-conscious habit builders</p>
  <p>🔒 Your data, your device, your habits</p>
</div>
