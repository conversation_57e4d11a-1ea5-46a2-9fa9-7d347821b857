# Contributing to <PERSON><PERSON><PERSON>

Thank you for your interest in contributing to <PERSON><PERSON><PERSON>! We welcome contributions from the community and are grateful for your help in making this privacy-first habit tracker even better.

## 🔒 Privacy First

Before contributing, please remember that Habiit is built with privacy as the core principle. All contributions should maintain this commitment:

- No data collection or tracking
- No external API calls (except for app store functionality)
- All data processing happens locally on the device
- No user identification or profiling

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm or yarn
- Expo CLI
- Git
- iOS Simulator (for iOS development)
- Android Studio/Emulator (for Android development)

### Setting up the Development Environment

1. **Fork the repository**
   ```bash
   git clone https://github.com/yourusername/habiit.git
   cd habiit
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Run tests to ensure everything works**
   ```bash
   npm test
   ```

## 📝 How to Contribute

### Reporting Issues

Before creating an issue, please:

1. **Search existing issues** to avoid duplicates
2. **Use the issue templates** when available
3. **Provide detailed information** including:
   - Device and OS version
   - App version
   - Steps to reproduce
   - Expected vs actual behavior
   - Screenshots if applicable

### Suggesting Features

We welcome feature suggestions! Please:

1. **Check existing feature requests** first
2. **Consider privacy implications** - ensure the feature aligns with our privacy-first approach
3. **Provide detailed use cases** and explain how it benefits users
4. **Consider implementation complexity** and maintenance burden

### Code Contributions

#### Development Workflow

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow the existing code style
   - Add tests for new functionality
   - Update documentation if needed

3. **Test your changes**
   ```bash
   npm test
   npm run lint
   ```

4. **Commit your changes**
   ```bash
   git commit -m "feat: add amazing new feature"
   ```

5. **Push to your fork**
   ```bash
   git push origin feature/your-feature-name
   ```

6. **Create a Pull Request**
   - Use the PR template
   - Provide clear description of changes
   - Link related issues
   - Add screenshots for UI changes

#### Code Style Guidelines

- **TypeScript** - Use TypeScript for all new code
- **ESLint** - Follow the existing ESLint configuration
- **Prettier** - Code formatting is handled automatically
- **Naming Conventions**:
  - Use camelCase for variables and functions
  - Use PascalCase for components and classes
  - Use UPPER_SNAKE_CASE for constants
  - Use descriptive names that explain purpose

#### Testing Guidelines

- **Write tests** for all new functionality
- **Update existing tests** when modifying behavior
- **Aim for high coverage** but focus on meaningful tests
- **Test edge cases** and error conditions
- **Use descriptive test names** that explain what is being tested

#### Component Guidelines

- **Keep components small** and focused on a single responsibility
- **Use TypeScript interfaces** for props
- **Follow React Native Paper** design patterns
- **Ensure accessibility** with proper labels and hints
- **Support both light and dark themes**

## 🌍 Localization

We welcome translations to new languages!

### Adding a New Language

1. **Create translation file**
   ```bash
   src/locales/{language-code}/common.json
   ```

2. **Translate all keys** from the English version

3. **Add language to i18n configuration**
   ```typescript
   // src/utils/i18n.ts
   const supportedLanguages = ['en', 'es', 'your-language'];
   ```

4. **Update getSupportedLanguages function**
   ```typescript
   { code: 'your-code', name: 'Language Name', nativeName: 'Native Name' }
   ```

5. **Test the translation** thoroughly

### Translation Guidelines

- **Keep translations concise** but clear
- **Maintain consistent tone** across the app
- **Consider cultural context** and local conventions
- **Test with longer text** to ensure UI doesn't break
- **Use gender-neutral language** when possible

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Writing Tests

- **Unit tests** for services and utilities
- **Component tests** for UI components
- **Integration tests** for complex workflows
- **Mock external dependencies** appropriately

### Test Structure

```typescript
describe('ComponentName', () => {
  beforeEach(() => {
    // Setup
  });

  it('should do something specific', () => {
    // Test implementation
  });
});
```

## 📱 Platform-Specific Considerations

### iOS
- Test on multiple iOS versions
- Ensure proper keychain usage
- Follow iOS Human Interface Guidelines
- Test with different screen sizes

### Android
- Test on multiple Android versions
- Ensure proper keystore usage
- Follow Material Design guidelines
- Test with different screen densities

### Web
- Ensure responsive design
- Test with different browsers
- Handle web-specific limitations
- Maintain feature parity where possible

## 🔍 Code Review Process

### For Contributors

- **Be responsive** to feedback
- **Explain your approach** in PR descriptions
- **Keep PRs focused** on a single feature/fix
- **Update documentation** as needed

### For Reviewers

- **Be constructive** and helpful
- **Focus on code quality** and maintainability
- **Consider privacy implications**
- **Test the changes** locally when possible

## 📋 Pull Request Checklist

Before submitting a PR, ensure:

- [ ] Code follows the style guidelines
- [ ] Tests are written and passing
- [ ] Documentation is updated
- [ ] Privacy principles are maintained
- [ ] No breaking changes (or properly documented)
- [ ] Localization is considered
- [ ] Accessibility is maintained
- [ ] Both light and dark themes work
- [ ] iOS and Android compatibility

## 🎯 Areas for Contribution

We especially welcome contributions in these areas:

- **New languages** for localization
- **Accessibility improvements**
- **Performance optimizations**
- **UI/UX enhancements**
- **Additional habit tracking features**
- **Better data visualization**
- **Bug fixes and stability improvements**

## 📞 Getting Help

If you need help with contributing:

- **Check the documentation** first
- **Search existing issues** and discussions
- **Ask questions** in GitHub Discussions
- **Join our community** discussions

## 🙏 Recognition

Contributors will be recognized in:

- **README.md** contributors section
- **Release notes** for significant contributions
- **Special thanks** in the app's about section

## 📄 License

By contributing to Habiit, you agree that your contributions will be licensed under the MIT License.

---

Thank you for helping make Habiit better while keeping privacy at the forefront! 🔒❤️
