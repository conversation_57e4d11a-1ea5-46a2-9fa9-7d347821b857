"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.Commands = void 0;
var _codegenNativeCommands = _interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeCommands"));
var _codegenNativeComponent = _interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeComponent"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const Commands = exports.Commands = (0, _codegenNativeCommands.default)({
  supportedCommands: ['setPage', 'setPageWithoutAnimation', 'setScrollEnabledImperatively']
});
var _default = exports.default = (0, _codegenNativeComponent.default)('RNCViewPager');
//# sourceMappingURL=PagerViewNativeComponent.js.map