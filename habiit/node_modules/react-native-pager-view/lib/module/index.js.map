{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["index.tsx"], "sourcesContent": ["import type * as ReactNative from 'react-native';\nimport { PagerView } from './PagerView';\nexport default PagerView;\nexport * from './usePagerView';\n\nimport type {\n  OnPageScrollEventData as PagerViewOnPageScrollEventData,\n  OnPageSelectedEventData as PagerViewOnPageSelectedEventData,\n  OnPageScrollStateChangedEventData as PageScrollStateChangedNativeEventData,\n  NativeProps,\n} from './PagerViewNativeComponent';\n\nexport type {\n  PagerViewOnPageScrollEventData,\n  PagerViewOnPageSelectedEventData,\n  PageScrollStateChangedNativeEventData,\n  NativeProps as PagerViewProps,\n};\n\nexport type PagerViewOnPageScrollEvent =\n  ReactNative.NativeSyntheticEvent<PagerViewOnPageScrollEventData>;\n\nexport type PagerViewOnPageSelectedEvent =\n  ReactNative.NativeSyntheticEvent<PagerViewOnPageSelectedEventData>;\n\nexport type PageScrollStateChangedNativeEvent =\n  ReactNative.NativeSyntheticEvent<PageScrollStateChangedNativeEventData>;\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,aAAa;AACvC,eAAeA,SAAS;AACxB,cAAc,gBAAgB", "ignoreList": []}