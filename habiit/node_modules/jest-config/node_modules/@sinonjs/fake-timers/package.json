{"name": "@sinonjs/fake-timers", "description": "Fake JavaScript timers", "version": "13.0.5", "homepage": "https://github.com/sinonjs/fake-timers", "author": "<PERSON>", "repository": {"type": "git", "url": "git+https://github.com/sinonjs/fake-timers.git"}, "bugs": {"mail": "<EMAIL>", "url": "https://github.com/sinonjs/fake-timers/issues"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"lint": "eslint .", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "test-headless": "mochify --driver puppeteer", "test-check-coverage": "npm run test-coverage && nyc check-coverage", "test-cloud": "npm run test-edge && npm run test-firefox && npm run test-safari", "test-edge": "BROWSER_NAME=MicrosoftEdge mochify --config mochify.webdriver.js", "test-firefox": "BROWSER_NAME=firefox mochify --config mochify.webdriver.js", "test-safari": "BROWSER_NAME=safari mochify --config mochify.webdriver.js", "test-coverage": "nyc -x mochify.webdriver.js -x coverage --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test": "npm run test-node && npm run test-headless", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "preversion": "./scripts/preversion.sh", "version": "./scripts/version.sh", "postversion": "./scripts/postversion.sh", "prepare": "husky"}, "lint-staged": {"*.{js,css,md}": "prettier --check", "*.js": "eslint"}, "mochify": {"reporter": "dot", "timeout": 10000, "bundle": "esbuild --bundle --sourcemap=inline --define:process.env.NODE_DEBUG=\"\"", "bundle_stdin": "require", "spec": "test/**/*-test.js"}, "files": ["src/"], "devDependencies": {"@mochify/cli": "^0.4.1", "@mochify/driver-puppeteer": "^0.4.0", "@mochify/driver-webdriver": "^0.2.1", "@sinonjs/eslint-config": "^5.0.3", "@sinonjs/referee-sinon": "12.0.0", "esbuild": "^0.23.1", "husky": "^9.1.5", "jsdom": "24.1.1", "lint-staged": "15.2.9", "mocha": "10.7.3", "nyc": "17.0.0", "prettier": "3.3.3"}, "main": "./src/fake-timers-src.js", "dependencies": {"@sinonjs/commons": "^3.0.1"}, "nyc": {"branches": 85, "lines": 92, "functions": 92, "statements": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"]}}