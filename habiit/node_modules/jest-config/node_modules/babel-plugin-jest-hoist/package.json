{"name": "babel-plugin-jest-hoist", "version": "30.0.1", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/babel-plugin-jest-hoist"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "@types/babel__core": "^7.20.5"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@prettier/sync": "^0.5.5", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.7", "@types/node": "*", "babel-plugin-tester": "^11.0.4", "prettier": "^3.0.3"}, "publishConfig": {"access": "public"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7"}