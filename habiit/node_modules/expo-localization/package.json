{"name": "expo-localization", "version": "16.1.6", "description": "Provides an interface for native user localization information.", "main": "build/Localization.js", "types": "build/Localization.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "localization", "locales", "l10n"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-localization"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "contributors": ["<PERSON> <<EMAIL>> (https://github.com/evanbacon)"], "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/localization/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"rtl-detect": "^1.0.2"}, "devDependencies": {"@types/rtl-detect": "^1.0.3", "expo-module-scripts": "^4.1.8"}, "peerDependencies": {"expo": "*", "react": "*"}, "gitHead": "9731a6191dcab84e9c3a24492bbe70c56d6f5cc3"}