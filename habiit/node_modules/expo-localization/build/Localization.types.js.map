{"version": 3, "file": "Localization.types.js", "sourceRoot": "", "sources": ["../src/Localization.types.ts"], "names": [], "mappings": "AA6FA;;GAEG;AACH,MAAM,CAAN,IAAY,OAQX;AARD,WAAY,OAAO;IACjB,yCAAU,CAAA;IACV,yCAAU,CAAA;IACV,2CAAW,CAAA;IACX,+CAAa,CAAA;IACb,6CAAY,CAAA;IACZ,yCAAU,CAAA;IACV,6CAAY,CAAA;AACd,CAAC,EARW,OAAO,KAAP,OAAO,QAQlB;AAED;;;GAGG;AACH,MAAM,CAAN,IAAY,kBAuCX;AAvCD,WAAY,kBAAkB;IAC5B,6BAA6B;IAC7B,2CAAqB,CAAA;IACrB,mCAAmC;IACnC,yCAAmB,CAAA;IACnB,sBAAsB;IACtB,uCAAiB,CAAA;IACjB,kCAAkC;IAClC,qCAAe,CAAA;IACf,+DAA+D;IAC/D,yCAAmB,CAAA;IACnB,6DAA6D;IAC7D,2CAAqB,CAAA;IACrB,yBAAyB;IACzB,yCAAmB,CAAA;IACnB,iCAAiC;IACjC,2CAAqB,CAAA;IACrB,kCAAkC;IAClC,uCAAiB,CAAA;IACjB,sBAAsB;IACtB,uCAAiB,CAAA;IACjB,uBAAuB;IACvB,yCAAmB,CAAA;IACnB,kGAAkG;IAClG,qDAA+B,CAAA;IAC/B,8CAA8C;IAC9C,mDAA6B,CAAA;IAC7B,wGAAwG;IACxG,mDAA6B,CAAA;IAC7B,oCAAoC;IACpC,2DAAqC,CAAA;IACrC,+EAA+E;IAC/E,yCAAmB,CAAA;IACnB,iCAAiC;IACjC,2CAAqB,CAAA;IACrB,uBAAuB;IACvB,yCAAmB,CAAA;IACnB,0CAA0C;IAC1C,iCAAW,CAAA;AACb,CAAC,EAvCW,kBAAkB,KAAlB,kBAAkB,QAuC7B", "sourcesContent": ["export type Locale = {\n  /**\n   * An [IETF BCP 47 language tag](https://en.wikipedia.org/wiki/IETF_language_tag) with a region code.\n   * @example\n   * `'en-US'`, `'es-419'`, `'pl-PL'`.\n   */\n  languageTag: string;\n  /**\n   * An [IETF BCP 47 language tag](https://en.wikipedia.org/wiki/IETF_language_tag) without the region code.\n   * @example\n   * `'en'`, `'es'`, `'pl'`.\n   */\n  languageCode: string | null;\n  /**\n   * An [ISO 15924](https://en.wikipedia.org/wiki/ISO_15924) 4-letter script code. On Android and Web, it may be `null` if none is defined.\n   * @example\n   * `'Latn'`, `'Hans'`, `'<PERSON>br'`.\n   */\n  languageScriptCode: string | null;\n  /**\n   * The region code for your device that comes from the Region setting under Language & Region on iOS, Region settings on Android and is parsed from locale on Web (can be `null` on Web).\n   * @example\n   * `'US'`.\n   */\n  regionCode: string | null;\n  /**\n   * The region code for the preferred language. When the language is not region-specific, it returns the same value as `regionCode`. When the language is region-specific, it returns the region code for the language (`en-CA` -> `CA`).\n   * Prefer using `regionCode` for any internalization purposes.\n   * @example\n   * `'US'`.\n   */\n  languageRegionCode: string | null;\n  /**\n   * Currency code for the locale.\n   * On iOS, it's the currency code from the `Region` setting under Language & Region, not for the current locale.\n   * On Android, it's the currency specifc to the locale in the list, as there are no separate settings for selecting a region.\n   * Is `null` on Web, use a table lookup based on region instead.\n   * @example\n   * `'USD'`, `'EUR'`, `'PLN'`.\n   */\n  currencyCode: string | null;\n  /**\n   * Currency symbol for the currency specified by `currencyCode`.\n   * @example\n   * `'$'`, `'€'`, `'zł'`.\n   */\n  currencySymbol: string | null;\n  /**\n   * Currency code for the locale.\n   * On iOS, it's the currency code for the current locale in the list, not the device region.\n   * On Android, it's equal to `currencyCode`.\n   * Is `null` on Web.\n   * Prefer using `currencyCode` for any internalization purposes.\n   * @example\n   * `'USD'`, `'EUR'`, `'PLN'`.\n   */\n  languageCurrencyCode: string | null;\n  /**\n   * Currency symbol for the currency specified by `languageCurrencyCode`.\n   * Prefer using `currencySymbol` for any internalization purposes.\n   * @example\n   * `'$'`, `'€'`, `'zł'`.\n   */\n  languageCurrencySymbol: string | null;\n  /**\n   * Decimal separator used for formatting numbers with fractional parts.\n   * @example\n   * `'.'`, `','`.\n   */\n  decimalSeparator: string | null;\n  /**\n   * Digit grouping separator used for formatting large numbers.\n   * @example\n   * `'.'`, `','`.\n   */\n  digitGroupingSeparator: string | null;\n  /**\n   * Text direction for the locale. One of: `'ltr'`, `'rtl'`, but can also be `null` on some browsers without support for the [textInfo](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/textInfo) property in [Intl](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl) API.\n   */\n  textDirection: 'ltr' | 'rtl' | null;\n  /**\n   * The measurement system used in the locale.\n   * Is `null` on Web, as user chosen measurement system is not exposed on the web and using locale to determine measurement systems is unreliable.\n   * Ask for user preferences if possible.\n   */\n  measurementSystem: `metric` | `us` | `uk` | null;\n  /**\n   * The temperature unit used in the locale.\n   * Returns `null` if the region code is unknown.\n   */\n  temperatureUnit: 'celsius' | 'fahrenheit' | null;\n};\n\n/**\n * An enum mapping days of the week in Gregorian calendar to their index as returned by the `firstWeekday` property.\n */\nexport enum Weekday {\n  SUNDAY = 1,\n  MONDAY = 2,\n  TUESDAY = 3,\n  WEDNESDAY = 4,\n  THURSDAY = 5,\n  FRIDAY = 6,\n  SATURDAY = 7,\n}\n\n/**\n * The calendar identifier, one of [Unicode calendar types](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/calendar).\n * Gregorian calendar is aliased and can be referred to as both `CalendarIdentifier.GREGORIAN` and `CalendarIdentifier.GREGORY`.\n */\nexport enum CalendarIdentifier {\n  /** Thai Buddhist calendar */\n  BUDDHIST = 'buddhist',\n  /** Traditional Chinese calendar */\n  CHINESE = 'chinese',\n  /** Coptic calendar */\n  COPTIC = 'coptic',\n  /** Traditional Korean calendar */\n  DANGI = 'dangi',\n  /** Ethiopic calendar, Amete Alem (epoch approx. 5493 B.C.E) */\n  ETHIOAA = 'ethioaa',\n  /** Ethiopic calendar, Amete Mihret (epoch approx, 8 C.E.) */\n  ETHIOPIC = 'ethiopic',\n  /** Gregorian calendar */\n  GREGORY = 'gregory',\n  /** Gregorian calendar (alias) */\n  GREGORIAN = 'gregory',\n  /** Traditional Hebrew calendar */\n  HEBREW = 'hebrew',\n  /** Indian calendar */\n  INDIAN = 'indian',\n  /** Islamic calendar */\n  ISLAMIC = 'islamic',\n  /** Islamic calendar, tabular (intercalary years [2,5,7,10,13,16,18,21,24,26,29] - civil epoch) */\n  ISLAMIC_CIVIL = 'islamic-civil',\n  /** Islamic calendar, Saudi Arabia sighting */\n  ISLAMIC_RGSA = 'islamic-rgsa',\n  /**Islamic calendar, tabular (intercalary years [2,5,7,10,13,16,18,21,24,26,29] - astronomical epoch) */\n  ISLAMIC_TBLA = 'islamic-tbla',\n  /** Islamic calendar, Umm al-Qura */\n  ISLAMIC_UMALQURA = 'islamic-umalqura',\n  /** ISO calendar (Gregorian calendar using the ISO 8601 calendar week rules) */\n  ISO8601 = 'iso8601',\n  /** Japanese imperial calendar */\n  JAPANESE = 'japanese',\n  /** Persian calendar */\n  PERSIAN = 'persian',\n  /** Civil (algorithmic) Arabic calendar */\n  ROC = 'roc',\n}\n\nexport type Calendar = {\n  /**\n   * The calendar identifier, one of [Unicode calendar types](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/calendar).\n   *\n   * On Android is limited to one of device's [available calendar types](https://developer.android.com/reference/java/util/Calendar#getAvailableCalendarTypes()).\n   *\n   * On iOS uses [calendar identifiers](https://developer.apple.com/documentation/foundation/calendar/identifier), but maps them to the corresponding Unicode types, will also never contain `'dangi'` or `'islamic-rgsa'` due to it not being implemented on iOS.\n   */\n  calendar: CalendarIdentifier | null;\n  /**\n   * True when current device settings use 24-hour time format.\n   * Can be null on some browsers that don't support the [hourCycle](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle) property in [Intl](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl) API.\n   */\n  uses24hourClock: boolean | null;\n  /**\n   * The first day of the week. For most calendars Sunday is numbered `1`, with Saturday being number `7`.\n   * Can be null on some browsers that don't support the [weekInfo](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/weekInfo) property in [Intl](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl) API.\n   * @example\n   * `1`, `7`.\n   */\n  firstWeekday: Weekday | null;\n  /**\n   * Time zone for the calendar. Can be `null` on Web.\n   * @example\n   * `'America/Los_Angeles'`, `'Europe/Warsaw'`, `'GMT+1'`.\n   */\n  timeZone: string | null;\n};\n"]}