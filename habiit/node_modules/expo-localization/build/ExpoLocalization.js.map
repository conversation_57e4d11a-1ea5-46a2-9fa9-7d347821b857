{"version": 3, "file": "ExpoLocalization.js", "sourceRoot": "", "sources": ["../src/ExpoLocalization.ts"], "names": [], "mappings": "AAAA,wBAAwB;AACxB,OAAO,EAAE,QAAQ,EAA0B,MAAM,mBAAmB,CAAC;AAIrE,MAAM,mBAAmB,GAAG,GAAG,EAAE;IAC/B,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;QAC5B,OAAO,SAAS,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IACrD,CAAC;IACD,MAAM,cAAc,GAAG,IAAI,EAAE,cAAc,EAAE,EAAE,eAAe,EAAE,EAAE,MAAM,CAAC;IACzE,IAAI,cAAc,EAAE,CAAC;QACnB,OAAO,CAAC,cAAc,CAAC,CAAC;IAC1B,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAaF,MAAM,yBAAyB,GAAG,gBAAgB,CAAC;AACnD,wEAAwE;AACxE,MAAM,eAAe,GAAG;IACtB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;CACL,CAAC;AAEF,MAAM,UAAU,iBAAiB;AAC/B,+CAA+C;AAC/C,QAAmC;IAEnC,gBAAgB,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC;IACtD,OAAO;QACL,MAAM,EAAE,GAAG,EAAE,CAAC,mBAAmB,CAAC,yBAAyB,EAAE,QAAQ,CAAC;KACvE,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,mBAAmB;AACjC,+CAA+C;AAC/C,QAAmC;IAEnC,gBAAgB,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC;IACtD,OAAO;QACL,MAAM,EAAE,GAAG,EAAE,CAAC,mBAAmB,CAAC,yBAAyB,EAAE,QAAQ,CAAC;KACvE,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,YAA+B;IAChE,YAAY,CAAC,MAAM,EAAE,CAAC;AACxB,CAAC;AAED,eAAe;IACb,UAAU;QACR,MAAM,OAAO,GAAG,mBAAmB,EAAE,CAAC;QACtC,OAAO,OAAO,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YAClC,yEAAyE;YACzE,iEAAiE;YAEjE,IAAI,MAAM,GAAG,EAAoB,CAAC;YAElC,qFAAqF;YACrF,IAAI,sBAAsB,GAAkB,IAAI,CAAC;YACjD,IAAI,gBAAgB,GAAkB,IAAI,CAAC;YAC3C,IAAI,eAAe,GAAoC,IAAI,CAAC;YAE5D,yEAAyE;YACzE,wDAAwD;YACxD,IAAI,CAAC;gBACH,sBAAsB;oBACpB,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;wBACpF,IAAI,CAAC,CAAC,+FAA+F;gBAEvG,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAErE,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE,CAAC;oBAChC,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAA8B,CAAC;gBACrE,CAAC;YACH,CAAC;YAAC,MAAM,CAAC,CAAA,CAAC;YAEV,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;YAEtD,IAAI,MAAM,EAAE,CAAC;gBACX,eAAe,GAAG,uBAAuB,CAAC,MAAM,CAAC,CAAC;YACpD,CAAC;YAED,OAAO;gBACL,WAAW;gBACX,YAAY,EAAE,QAAQ,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;gBAC3D,kBAAkB,EAAE,MAAM,IAAI,IAAI;gBAClC,aAAa,EAAG,QAAQ,EAAE,SAA2B,IAAI,IAAI;gBAC7D,sBAAsB;gBACtB,gBAAgB;gBAChB,iBAAiB,EAAE,IAAI;gBACvB,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,IAAI;gBACpB,oBAAoB,EAAE,IAAI;gBAC1B,sBAAsB,EAAE,IAAI;gBAC5B,wIAAwI;gBACxI,UAAU,EAAE,MAAM,IAAI,IAAI;gBAC1B,kBAAkB,EAAE,MAAM,IAAI,IAAI;gBAClC,eAAe;aAChB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IACD,YAAY;QACV,MAAM,MAAM,GAAG,CAAC,CAAC,OAAO,IAAI,KAAK,WAAW;YAC1C,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,eAAe,EAAE;YACzC,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAqC,CAAC;QACvD,OAAO;YACL;gBACE,QAAQ,EAAG,CAAC,MAAM,EAAE,QAAQ,IAAI,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAwB,IAAI,IAAI;gBACtF,QAAQ,EAAE,MAAM,EAAE,QAAQ,IAAI,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI;gBAC5D,eAAe,EAAE,CAAC,MAAM,EAAE,SAAS,IAAI,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,wGAAwG;gBACnM,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,IAAI,IAAI;aACjD;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,uBAAuB,CAAC,MAAc;IAC7C,OAAO,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC;AACrE,CAAC", "sourcesContent": ["/* eslint-env browser */\nimport { Platform, type EventSubscription } from 'expo-modules-core';\n\nimport { Calendar, Locale, CalendarIdentifier } from './Localization.types';\n\nconst getNavigatorLocales = () => {\n  if (Platform.isDOMAvailable) {\n    return navigator.languages || [navigator.language];\n  }\n  const dtFormatLocale = Intl?.DateTimeFormat()?.resolvedOptions()?.locale;\n  if (dtFormatLocale) {\n    return [dtFormatLocale];\n  }\n  return [];\n};\n\ntype ExtendedLocale = Intl.Locale &\n  // typescript definitions for navigator language don't include some modern Intl properties\n  Partial<{\n    textInfo: { direction: 'ltr' | 'rtl' };\n    timeZones: string[];\n    weekInfo: { firstDay: number };\n    hourCycles: string[];\n    timeZone: string;\n    calendars: string[];\n  }>;\n\nconst WEB_LANGUAGE_CHANGE_EVENT = 'languagechange';\n// https://wisevoter.com/country-rankings/countries-that-use-fahrenheit/\nconst USES_FAHRENHEIT = [\n  'AG',\n  'BZ',\n  'VG',\n  'FM',\n  'MH',\n  'MS',\n  'KN',\n  'BS',\n  'CY',\n  'TC',\n  'US',\n  'LR',\n  'PW',\n  'KY',\n];\n\nexport function addLocaleListener(\n  // NOTE(@kitten): We never use the event's data\n  listener: (event?: unknown) => void\n): EventSubscription {\n  addEventListener(WEB_LANGUAGE_CHANGE_EVENT, listener);\n  return {\n    remove: () => removeEventListener(WEB_LANGUAGE_CHANGE_EVENT, listener),\n  };\n}\n\nexport function addCalendarListener(\n  // NOTE(@kitten): We never use the event's data\n  listener: (event?: unknown) => void\n): EventSubscription {\n  addEventListener(WEB_LANGUAGE_CHANGE_EVENT, listener);\n  return {\n    remove: () => removeEventListener(WEB_LANGUAGE_CHANGE_EVENT, listener),\n  };\n}\n\nexport function removeSubscription(subscription: EventSubscription) {\n  subscription.remove();\n}\n\nexport default {\n  getLocales(): Locale[] {\n    const locales = getNavigatorLocales();\n    return locales?.map((languageTag) => {\n      // TextInfo is an experimental API that is not available in all browsers.\n      // We might want to consider using a locale lookup table instead.\n\n      let locale = {} as ExtendedLocale;\n\n      // Properties added only for compatibility with native, use `toLocaleString` instead.\n      let digitGroupingSeparator: string | null = null;\n      let decimalSeparator: string | null = null;\n      let temperatureUnit: 'fahrenheit' | 'celsius' | null = null;\n\n      // Gracefully handle language codes like `en-GB-oed` which is unsupported\n      // but is otherwise a valid language tag (grandfathered)\n      try {\n        digitGroupingSeparator =\n          Array.from((10000).toLocaleString(languageTag)).filter((c) => c > '9' || c < '0')[0] ||\n          null; // using 1e5 instead of 1e4 since for some locales (like pl-PL) 1e4 does not use digit grouping\n\n        decimalSeparator = (1.1).toLocaleString(languageTag).substring(1, 2);\n\n        if (typeof Intl !== 'undefined') {\n          locale = new Intl.Locale(languageTag) as unknown as ExtendedLocale;\n        }\n      } catch {}\n\n      const { region, textInfo, language, script } = locale;\n\n      if (region) {\n        temperatureUnit = regionToTemperatureUnit(region);\n      }\n\n      return {\n        languageTag,\n        languageCode: language || languageTag.split('-')[0] || 'en',\n        languageScriptCode: script || null,\n        textDirection: (textInfo?.direction as 'ltr' | 'rtl') || null,\n        digitGroupingSeparator,\n        decimalSeparator,\n        measurementSystem: null,\n        currencyCode: null,\n        currencySymbol: null,\n        languageCurrencyCode: null,\n        languageCurrencySymbol: null,\n        // On web, we don't have a way to get the region code, except from the language tag. `regionCode` and `languageRegionCode` are the same.\n        regionCode: region || null,\n        languageRegionCode: region || null,\n        temperatureUnit,\n      };\n    });\n  },\n  getCalendars(): Calendar[] {\n    const locale = ((typeof Intl !== 'undefined'\n      ? Intl.DateTimeFormat().resolvedOptions()\n      : null) ?? null) as unknown as null | ExtendedLocale;\n    return [\n      {\n        calendar: ((locale?.calendar || locale?.calendars?.[0]) as CalendarIdentifier) || null,\n        timeZone: locale?.timeZone || locale?.timeZones?.[0] || null,\n        uses24hourClock: (locale?.hourCycle || locale?.hourCycles?.[0])?.startsWith('h2') ?? null, //https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle\n        firstWeekday: locale?.weekInfo?.firstDay || null,\n      },\n    ];\n  },\n};\n\nfunction regionToTemperatureUnit(region: string) {\n  return USES_FAHRENHEIT.includes(region) ? 'fahrenheit' : 'celsius';\n}\n"]}