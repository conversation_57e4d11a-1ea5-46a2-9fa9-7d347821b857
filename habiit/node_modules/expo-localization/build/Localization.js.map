{"version": 3, "file": "Localization.js", "sourceRoot": "", "sources": ["../src/Localization.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AAEvD,OAAO,gBAAgB,EAAE,EACvB,mBAAmB,EACnB,iBAAiB,EACjB,kBAAkB,GACnB,MAAM,oBAAoB,CAAC;AAE5B,cAAc,sBAAsB,CAAC;AAErC;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,gBAAgB,CAAC,UAAU,CAAC;AAEtD;;;;;;;;;;;;;GAaG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,gBAAgB,CAAC,YAAY,CAAC;AAE1D;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,MAAM,UAAU,UAAU;IACxB,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACtD,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACnD,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,YAAY,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC;QACnD,OAAO,GAAG,EAAE;YACV,kBAAkB,CAAC,YAAY,CAAC,CAAC;QACnC,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IACP,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;;;;;;;;;;;GAcG;AACH,MAAM,UAAU,YAAY;IAC1B,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACtD,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACvD,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,YAAY,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;QACrD,OAAO,GAAG,EAAE;YACV,kBAAkB,CAAC,YAAY,CAAC,CAAC;QACnC,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IACP,OAAO,SAAS,CAAC;AACnB,CAAC", "sourcesContent": ["import { useEffect, useReducer, useMemo } from 'react';\n\nimport ExpoLocalization, {\n  addCalendarListener,\n  addLocaleListener,\n  removeSubscription,\n} from './ExpoLocalization';\n\nexport * from './Localization.types';\n\n/**\n * List of user's locales, returned as an array of objects of type `Locale`.\n * Guaranteed to contain at least 1 element.\n * These are returned in the order the user defines in their device settings.\n * On the web currency and measurements systems are not provided, instead returned as null.\n * If needed, you can infer them from the current region using a lookup table.\n * @example\n * ```js\n * [{\n *   \"languageTag\": \"pl-PL\",\n *   \"languageCode\": \"pl\",\n *   \"textDirection\": \"ltr\",\n *   \"digitGroupingSeparator\": \" \",\n *   \"decimalSeparator\": \",\",\n *   \"measurementSystem\": \"metric\",\n *   \"currencyCode\": \"PLN\",\n *   \"currencySymbol\": \"zł\",\n *   \"regionCode\": \"PL\",\n *   \"temperatureUnit\": \"celsius\"\n * }]\n * ```\n */\nexport const getLocales = ExpoLocalization.getLocales;\n\n/**\n * List of user's preferred calendars, returned as an array of objects of type `Calendar`.\n * Guaranteed to contain at least 1 element.\n * For now always returns a single element, but it's likely to return a user preference list on some platforms in the future.\n * @example\n * ```js\n * [{\n *   \"calendar\": \"gregory\",\n *   \"timeZone\": \"Europe/Warsaw\",\n *   \"uses24hourClock\": true,\n *   \"firstWeekday\": 1\n * }]\n * ```\n */\nexport const getCalendars = ExpoLocalization.getCalendars;\n\n/**\n * A hook providing a list of user's locales, returned as an array of objects of type `Locale`.\n * Guaranteed to contain at least 1 element.\n * These are returned in the order the user defines in their device settings.\n * On the web currency and measurements systems are not provided, instead returned as null.\n * If needed, you can infer them from the current region using a lookup table.\n * If the OS settings change, the hook will rerender with a new list of locales.\n * @example\n * ```js\n * [{\n *   \"languageTag\": \"pl-PL\",\n *   \"languageCode\": \"pl\",\n *   \"textDirection\": \"ltr\",\n *   \"digitGroupingSeparator\": \" \",\n *   \"decimalSeparator\": \",\",\n *   \"measurementSystem\": \"metric\",\n *   \"currencyCode\": \"PLN\",\n *   \"currencySymbol\": \"zł\",\n *   \"regionCode\": \"PL\",\n *   \"temperatureUnit\": \"celsius\"\n * }]\n * ```\n */\nexport function useLocales() {\n  const [key, invalidate] = useReducer((k) => k + 1, 0);\n  const locales = useMemo(() => getLocales(), [key]);\n  useEffect(() => {\n    const subscription = addLocaleListener(invalidate);\n    return () => {\n      removeSubscription(subscription);\n    };\n  }, []);\n  return locales;\n}\n\n/**\n * A hook providing a list of user's preferred calendars, returned as an array of objects of type `Calendar`.\n * Guaranteed to contain at least 1 element.\n * For now always returns a single element, but it's likely to return a user preference list on some platforms in the future.\n * If the OS settings change, the hook will rerender with a new list of calendars.\n * @example\n * ```js\n * [{\n *   \"calendar\": \"gregory\",\n *   \"timeZone\": \"Europe/Warsaw\",\n *   \"uses24hourClock\": true,\n *   \"firstWeekday\": 1\n * }]\n * ```\n */\nexport function useCalendars() {\n  const [key, invalidate] = useReducer((k) => k + 1, 0);\n  const calendars = useMemo(() => getCalendars(), [key]);\n  useEffect(() => {\n    const subscription = addCalendarListener(invalidate);\n    return () => {\n      removeSubscription(subscription);\n    };\n  }, []);\n  return calendars;\n}\n"]}