/**
 * Automatically generated by expo-modules-test-core.
 *
 * This autogenerated file provides a mock for native Expo module,
 * and works out of the box with the expo jest preset.
 *  */

import { Locale, Calendar, CalendarIdentifier } from '../src/Localization.types';

export function getLocales(): [Locale] {
  return [
    {
      languageTag: 'en-US',
      languageCode: 'en',
      languageScriptCode: 'Latn',
      regionCode: 'US',
      languageRegionCode: 'US',
      currencyCode: 'USD',
      currencySymbol: '$',
      languageCurrencyCode: 'USD',
      languageCurrencySymbol: '$',
      decimalSeparator: '.',
      digitGroupingSeparator: ',',
      textDirection: 'ltr',
      measurementSystem: 'us',
      temperatureUnit: 'fahrenheit',
    },
  ];
}

export function getCalendars(): [Calendar] {
  return [
    {
      calendar: CalendarIdentifier.GREGORY,
      uses24hourClock: true,
      firstWeekday: 1,
      timeZone: 'America/New_York',
    },
  ];
}
