{"name": "rtl-detect", "version": "1.1.2", "description": "Library will help you to detect if the locale is right-to-left language.", "main": "index.js", "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "jest --coverage", "commit": "git-cz"}, "jest": {"verbose": true}, "repository": {"type": "git", "url": "git+https://github.com/shadiabuhilal/rtl-detect.git"}, "keywords": ["rtl-detect", "rtl-detect", "locale direction", "locale dir", "locale", "intl direction", "intl dir", "intl", "right-to-left", "left-to-right", "rtl", "ltr", "intl-lang"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (http://github.com/shadiabuhilal)", "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/shadiabuhilal/rtl-detect", "bugs": {"url": "https://github.com/shadiabuhilal/rtl-detect/issues"}, "licenses": [{"type": "BSD-3-<PERSON><PERSON>", "url": "https://github.com/shadiabuhilal/rtl-detect/blob/master/LICENSE.md"}], "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/release-notes-generator": "^12.0.0", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^7.26.0", "jest": "^26.6.3", "semantic-release": "^22.0.5"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}