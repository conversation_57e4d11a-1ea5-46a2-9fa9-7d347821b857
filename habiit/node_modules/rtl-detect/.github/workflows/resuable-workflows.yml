name: Reusable workflow

on:
  workflow_call:
    inputs:
      node-version:
        required: true
        type: string
    #secrets:
      #token:
        #required: true

jobs:
  build:

    runs-on: ubuntu-latest
    
    strategy:
      fail-fast: false
      matrix:
        node-version: ${{ from<PERSON><PERSON>(inputs.node-version) }}
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
    - name: Checkout 
      uses: actions/checkout@v3
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        registry-url: https://registry.npmjs.org/
        cache: 'npm'
    - name: Install dependencies
      run: npm ci
    - name: Test
      run: npm test
