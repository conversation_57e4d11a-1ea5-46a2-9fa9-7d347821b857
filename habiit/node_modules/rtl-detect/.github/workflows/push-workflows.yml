# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: push job

on:
  push:
    branches: [ "master" ]

jobs:
  call-resuable-workflows:
    uses: ./.github/workflows/resuable-workflows.yml
    with:
      node-version: '["20.x"]'
  
  Publish:
    needs: call-resuable-workflows
    runs-on: ubuntu-latest
    permissions:
      contents: write # to be able to publish a GitHub release
      issues: write # to be able to comment on released issues
      pull-requests: write # to be able to comment on released pull requests
      id-token: write # to enable use of OIDC for npm provenance
    strategy:
      fail-fast: false
      matrix:
        node-version: ["20.x"]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/
    steps:
    - name: Checkout 
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        registry-url: https://registry.npmjs.org/
        cache: 'npm'
    - name: Install dependencies
      run: npm clean-install
    - name: Verify the integrity of provenance attestations and registry signatures for installed dependencies
      run: npm audit signatures
    - name: Release
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        NODE_AUTH_TOKEN: ${{ secrets.NPMJS_ACCESS_TOKEN }}
      run: npx semantic-release
