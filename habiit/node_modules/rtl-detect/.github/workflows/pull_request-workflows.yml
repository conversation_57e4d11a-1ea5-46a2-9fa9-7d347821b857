# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: pull_request job

on:
  pull_request:
    branches: '*'

jobs:  
  call-resuable-workflows:
    uses: ./.github/workflows/resuable-workflows.yml
    with:
      node-version: '["16.x", "18.x", "20.x"]'
    # secrets:
    #  envPAT: ${{ secrets.envPAT }}
