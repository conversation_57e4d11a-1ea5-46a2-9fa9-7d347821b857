export interface Habit {
  id: string;
  title: string;
  emoji?: string;
  icons?: string[]; // Multiple icons support
  color: string;
  frequency: HabitFrequency;
  customDays?: number[]; // 0-6 for Sunday-Saturday, used when frequency is 'custom'
  reminderTime?: string; // HH:MM format
  reminderEnabled: boolean;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  isActive: boolean;
  // For multiple times per day
  dailyTarget?: number; // How many times per day
  reminderInterval?: number; // Minutes between reminders
  reminderTimes?: string[]; // Specific times for multiple reminders
}

export interface HabitCompletion {
  id: string;
  habitId: string;
  date: string; // YYYY-MM-DD format
  completedAt: string; // ISO date string
  completionIndex?: number; // For multiple completions per day (0, 1, 2, etc.)
}

export interface HabitStreak {
  habitId: string;
  currentStreak: number;
  longestStreak: number;
  lastCompletedDate?: string; // YYYY-MM-DD format
}

export interface HabitStatistics {
  habitId: string;
  totalCompletions: number;
  completionRate: number; // 0-1
  averageWeeklyCompletions: number;
  streak: HabitStreak;
  monthlyData: MonthlyHabitData[];
}

export interface MonthlyHabitData {
  month: string; // YYYY-MM format
  completions: number;
  expectedCompletions: number;
  completionRate: number;
}

export type HabitFrequency = 'daily' | 'weekly' | 'custom' | 'multiple_daily';

export interface HabitFormData {
  title: string;
  emoji?: string;
  icons?: string[]; // Multiple icons support
  color: string;
  frequency: HabitFrequency;
  customDays?: number[];
  reminderTime?: string;
  reminderEnabled: boolean;
  // For multiple times per day
  dailyTarget?: number; // How many times per day
  reminderInterval?: number; // Minutes between reminders
  reminderTimes?: string[]; // Specific times for multiple reminders
}

// Predefined colors for habits
export const HABIT_COLORS = [
  '#FF6B6B', // Red
  '#4ECDC4', // Teal
  '#45B7D1', // Blue
  '#96CEB4', // Green
  '#FFEAA7', // Yellow
  '#DDA0DD', // Plum
  '#98D8C8', // Mint
  '#F7DC6F', // Light Yellow
  '#BB8FCE', // Light Purple
  '#85C1E9', // Light Blue
] as const;

export type HabitColor = typeof HABIT_COLORS[number];
