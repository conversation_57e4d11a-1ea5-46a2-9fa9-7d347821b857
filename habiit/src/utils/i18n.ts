import { getLocales } from 'expo-localization';
import { storageService } from '../services/storage';
import { STORAGE_KEYS } from '../types/app';

// Import translation files
import enTranslations from '../locales/en/common.json';
import esTranslations from '../locales/es/common.json';

export type TranslationKey = string;
export type TranslationParams = Record<string, string | number>;

interface Translations {
  [key: string]: any;
}

const translations: Record<string, Translations> = {
  en: enTranslations,
  es: esTranslations,
};

const supportedLanguages = ['en', 'es'];
const fallbackLanguage = 'en';

/**
 * Get the current language from storage or device locale
 */
export const getCurrentLanguage = async (): Promise<string> => {
  try {
    const settings = await storageService.getSecureItem(STORAGE_KEYS.SETTINGS);
    if (settings?.language && supportedLanguages.includes(settings.language)) {
      return settings.language;
    }
  } catch (error) {
    console.error('Failed to get language from storage:', error);
  }

  // Fallback to device locale
  const deviceLocales = getLocales();
  const deviceLanguage = deviceLocales[0]?.languageCode;
  
  if (deviceLanguage && supportedLanguages.includes(deviceLanguage)) {
    return deviceLanguage;
  }

  return fallbackLanguage;
};

/**
 * Get nested value from object using dot notation
 */
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
};

/**
 * Replace placeholders in translation string
 */
const replacePlaceholders = (text: string, params?: TranslationParams): string => {
  if (!params) return text;
  
  return Object.keys(params).reduce((result, key) => {
    const placeholder = `{{${key}}}`;
    return result.replace(new RegExp(placeholder, 'g'), String(params[key]));
  }, text);
};

/**
 * Get translation for a key
 */
export const t = async (key: TranslationKey, params?: TranslationParams): Promise<string> => {
  try {
    const language = await getCurrentLanguage();
    const languageTranslations = translations[language] || translations[fallbackLanguage];
    
    let translation = getNestedValue(languageTranslations, key);
    
    // Fallback to English if translation not found
    if (translation === undefined && language !== fallbackLanguage) {
      translation = getNestedValue(translations[fallbackLanguage], key);
    }
    
    // Return key if no translation found
    if (translation === undefined) {
      console.warn(`Translation not found for key: ${key}`);
      return key;
    }
    
    // Handle array translations (like motivational messages)
    if (Array.isArray(translation)) {
      const randomIndex = Math.floor(Math.random() * translation.length);
      translation = translation[randomIndex];
    }
    
    return replacePlaceholders(translation, params);
  } catch (error) {
    console.error('Translation error:', error);
    return key;
  }
};

/**
 * Synchronous translation function (requires language to be passed)
 */
export const tSync = (key: TranslationKey, language: string = fallbackLanguage, params?: TranslationParams): string => {
  try {
    const languageTranslations = translations[language] || translations[fallbackLanguage];
    
    let translation = getNestedValue(languageTranslations, key);
    
    // Fallback to English if translation not found
    if (translation === undefined && language !== fallbackLanguage) {
      translation = getNestedValue(translations[fallbackLanguage], key);
    }
    
    // Return key if no translation found
    if (translation === undefined) {
      console.warn(`Translation not found for key: ${key}`);
      return key;
    }
    
    // Handle array translations
    if (Array.isArray(translation)) {
      const randomIndex = Math.floor(Math.random() * translation.length);
      translation = translation[randomIndex];
    }
    
    return replacePlaceholders(translation, params);
  } catch (error) {
    console.error('Translation error:', error);
    return key;
  }
};

/**
 * Set the current language
 */
export const setLanguage = async (language: string): Promise<void> => {
  if (!supportedLanguages.includes(language)) {
    console.warn(`Unsupported language: ${language}`);
    return;
  }

  try {
    const settings = await storageService.getSecureItem(STORAGE_KEYS.SETTINGS) || {
      theme: 'system',
      language: fallbackLanguage,
      notificationsEnabled: true,
      defaultReminderTime: '09:00',
      isPremium: false,
      onboardingCompleted: false,
    };

    const updatedSettings = { ...settings, language };
    await storageService.setSecureItem(STORAGE_KEYS.SETTINGS, updatedSettings);
  } catch (error) {
    console.error('Failed to set language:', error);
    throw new Error('Failed to set language');
  }
};

/**
 * Get list of supported languages
 */
export const getSupportedLanguages = (): Array<{ code: string; name: string; nativeName: string }> => {
  return [
    { code: 'en', name: 'English', nativeName: 'English' },
    { code: 'es', name: 'Spanish', nativeName: 'Español' },
  ];
};

/**
 * Get language display name
 */
export const getLanguageDisplayName = (languageCode: string): string => {
  const language = getSupportedLanguages().find(lang => lang.code === languageCode);
  return language ? language.nativeName : languageCode;
};

/**
 * React hook for translations
 */
export const useTranslation = () => {
  const [currentLanguage, setCurrentLanguage] = React.useState<string>(fallbackLanguage);

  React.useEffect(() => {
    getCurrentLanguage().then(setCurrentLanguage);
  }, []);

  const translate = React.useCallback((key: TranslationKey, params?: TranslationParams): string => {
    return tSync(key, currentLanguage, params);
  }, [currentLanguage]);

  const changeLanguage = React.useCallback(async (language: string) => {
    await setLanguage(language);
    setCurrentLanguage(language);
  }, []);

  return {
    t: translate,
    language: currentLanguage,
    changeLanguage,
    supportedLanguages: getSupportedLanguages(),
  };
};

// For React import
import React from 'react';
