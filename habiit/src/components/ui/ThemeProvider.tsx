import React, { createContext, useContext, useEffect, useState } from 'react';
import { useColorScheme } from 'react-native';
import { MD3LightTheme, MD3DarkTheme, PaperProvider, configureFonts } from 'react-native-paper';
import { AppSettings } from '../../types/app';
import { storageService } from '../../services/storage';
import { STORAGE_KEYS } from '../../types/app';

// Custom theme colors - Vibrant Gamified Light Theme
const lightColors = {
  ...MD3LightTheme.colors,
  primary: '#7C3AED', // Electric Purple
  primaryContainer: '#EDE9FE',
  secondary: '#06D6A0', // Bright Teal
  secondaryContainer: '#ECFDF5',
  tertiary: '#FF6B35', // Vibrant Orange
  tertiaryContainer: '#FFF7ED',
  surface: '#FFFFFF',
  surfaceVariant: '#F8FAFC',
  background: '#FAFAFA', // Slightly off-white for warmth
  error: '#FF4757',
  errorContainer: '#FEE2E2',
  onPrimary: '#FFFFFF',
  onSecondary: '#FFFFFF',
  onTertiary: '#FFFFFF',
  onSurface: '#1A202C',
  onSurfaceVariant: '#4A5568',
  onBackground: '#1A202C',
  // Custom gamification colors
  success: '#06D6A0', // Bright success green
  warning: '#FFB800', // Golden yellow
  info: '#4299E1', // Bright blue
  achievement: '#F093FB', // Pink gradient start
  streak: '#FF6B35', // Fire orange
  level: '#7C3AED', // Purple for levels
  gem: '#06D6A0', // Teal for gems/rewards
};

const darkColors = {
  ...MD3DarkTheme.colors,
  primary: '#A78BFA', // Bright Purple
  primaryContainer: '#5B21B6',
  secondary: '#4FD1C7', // Bright Cyan
  secondaryContainer: '#065F46',
  tertiary: '#FF8A65', // Bright Coral
  tertiaryContainer: '#BF360C',
  surface: '#1A202C', // Rich dark surface
  surfaceVariant: '#2D3748',
  background: '#0D1117', // Deep space black
  error: '#FF6B6B',
  errorContainer: '#C53030',
  onPrimary: '#FFFFFF',
  onSecondary: '#FFFFFF',
  onTertiary: '#FFFFFF',
  onSurface: '#F7FAFC',
  onSurfaceVariant: '#E2E8F0',
  onBackground: '#F7FAFC',
  // Custom gamification colors
  success: '#4FD1C7', // Bright cyan success
  warning: '#FFD93D', // Bright yellow
  info: '#74B9FF', // Bright blue
  achievement: '#FF7675', // Pink for achievements
  streak: '#FF8A65', // Bright coral for streaks
  level: '#A78BFA', // Purple for levels
  gem: '#4FD1C7', // Cyan for gems/rewards
};

// Custom fonts configuration
const fontConfig = {
  displayLarge: {
    fontFamily: 'System',
    fontSize: 57,
    fontWeight: '400' as const,
    letterSpacing: 0,
    lineHeight: 64,
  },
  displayMedium: {
    fontFamily: 'System',
    fontSize: 45,
    fontWeight: '400' as const,
    letterSpacing: 0,
    lineHeight: 52,
  },
  displaySmall: {
    fontFamily: 'System',
    fontSize: 36,
    fontWeight: '400' as const,
    letterSpacing: 0,
    lineHeight: 44,
  },
  headlineLarge: {
    fontFamily: 'System',
    fontSize: 32,
    fontWeight: '600' as const,
    letterSpacing: 0,
    lineHeight: 40,
  },
  headlineMedium: {
    fontFamily: 'System',
    fontSize: 28,
    fontWeight: '600' as const,
    letterSpacing: 0,
    lineHeight: 36,
  },
  headlineSmall: {
    fontFamily: 'System',
    fontSize: 24,
    fontWeight: '600' as const,
    letterSpacing: 0,
    lineHeight: 32,
  },
  titleLarge: {
    fontFamily: 'System',
    fontSize: 22,
    fontWeight: '500' as const,
    letterSpacing: 0,
    lineHeight: 28,
  },
  titleMedium: {
    fontFamily: 'System',
    fontSize: 16,
    fontWeight: '500' as const,
    letterSpacing: 0.15,
    lineHeight: 24,
  },
  titleSmall: {
    fontFamily: 'System',
    fontSize: 14,
    fontWeight: '500' as const,
    letterSpacing: 0.1,
    lineHeight: 20,
  },
  bodyLarge: {
    fontFamily: 'System',
    fontSize: 16,
    fontWeight: '400' as const,
    letterSpacing: 0.15,
    lineHeight: 24,
  },
  bodyMedium: {
    fontFamily: 'System',
    fontSize: 14,
    fontWeight: '400' as const,
    letterSpacing: 0.25,
    lineHeight: 20,
  },
  bodySmall: {
    fontFamily: 'System',
    fontSize: 12,
    fontWeight: '400' as const,
    letterSpacing: 0.4,
    lineHeight: 16,
  },
  labelLarge: {
    fontFamily: 'System',
    fontSize: 14,
    fontWeight: '500' as const,
    letterSpacing: 0.1,
    lineHeight: 20,
  },
  labelMedium: {
    fontFamily: 'System',
    fontSize: 12,
    fontWeight: '500' as const,
    letterSpacing: 0.5,
    lineHeight: 16,
  },
  labelSmall: {
    fontFamily: 'System',
    fontSize: 11,
    fontWeight: '500' as const,
    letterSpacing: 0.5,
    lineHeight: 16,
  },
};

const lightTheme = {
  ...MD3LightTheme,
  colors: lightColors,
  fonts: configureFonts({ config: fontConfig }),
};

const darkTheme = {
  ...MD3DarkTheme,
  colors: darkColors,
  fonts: configureFonts({ config: fontConfig }),
};

interface ThemeContextType {
  isDark: boolean;
  theme: typeof lightTheme;
  toggleTheme: () => void;
  setThemeMode: (mode: 'light' | 'dark' | 'system') => void;
  themeMode: 'light' | 'dark' | 'system';
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useAppTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useAppTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const systemColorScheme = useColorScheme();
  const [themeMode, setThemeMode] = useState<'light' | 'dark' | 'system'>('system');
  const [isDark, setIsDark] = useState(systemColorScheme === 'dark');

  useEffect(() => {
    loadThemeSettings();
  }, []);

  useEffect(() => {
    if (themeMode === 'system') {
      setIsDark(systemColorScheme === 'dark');
    } else {
      setIsDark(themeMode === 'dark');
    }
  }, [themeMode, systemColorScheme]);

  const loadThemeSettings = async () => {
    try {
      const settings = await storageService.getSecureItem<AppSettings>(STORAGE_KEYS.SETTINGS);
      if (settings?.theme) {
        setThemeMode(settings.theme);
      }
    } catch (error) {
      console.error('Failed to load theme settings:', error);
    }
  };

  const saveThemeSettings = async (mode: 'light' | 'dark' | 'system') => {
    try {
      const settings = await storageService.getSecureItem<AppSettings>(STORAGE_KEYS.SETTINGS) || {
        theme: 'system',
        language: 'en',
        notificationsEnabled: true,
        defaultReminderTime: '09:00',
        isPremium: false,
        onboardingCompleted: false,
      };
      
      const updatedSettings = { ...settings, theme: mode };
      await storageService.setSecureItem(STORAGE_KEYS.SETTINGS, updatedSettings);
    } catch (error) {
      console.error('Failed to save theme settings:', error);
    }
  };

  const toggleTheme = () => {
    const newMode = isDark ? 'light' : 'dark';
    setThemeMode(newMode);
    saveThemeSettings(newMode);
  };

  const handleSetThemeMode = (mode: 'light' | 'dark' | 'system') => {
    setThemeMode(mode);
    saveThemeSettings(mode);
  };

  const theme = isDark ? darkTheme : lightTheme;

  const contextValue: ThemeContextType = {
    isDark,
    theme,
    toggleTheme,
    setThemeMode: handleSetThemeMode,
    themeMode,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <PaperProvider theme={theme}>
        {children}
      </PaperProvider>
    </ThemeContext.Provider>
  );
};
