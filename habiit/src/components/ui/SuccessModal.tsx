import React from 'react';
import { View, StyleSheet } from 'react-native';
import { 
  Modal, 
  Portal, 
  Text, 
  Button, 
  useTheme,
  Card,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring, 
  withTiming,
  runOnJS,
} from 'react-native-reanimated';

interface SuccessModalProps {
  visible: boolean;
  onDismiss: () => void;
  title: string;
  message: string;
  primaryAction: {
    label: string;
    onPress: () => void;
  };
  secondaryAction?: {
    label: string;
    onPress: () => void;
  };
  icon?: string;
}

export const SuccessModal: React.FC<SuccessModalProps> = ({
  visible,
  onDismiss,
  title,
  message,
  primaryAction,
  secondaryAction,
  icon = 'check-circle',
}) => {
  const theme = useTheme();
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);

  React.useEffect(() => {
    if (visible) {
      opacity.value = withTiming(1, { duration: 200 });
      scale.value = withSpring(1, { 
        damping: 15,
        stiffness: 150,
      });
    } else {
      opacity.value = withTiming(0, { duration: 150 });
      scale.value = withTiming(0.8, { duration: 150 }, () => {
        runOnJS(onDismiss)();
      });
    }
  }, [visible]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }],
  }));

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.modalContainer,
          { backgroundColor: 'rgba(0,0,0,0.5)' }
        ]}
      >
        <Animated.View style={animatedStyle}>
          <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
            <Card.Content style={styles.content}>
              {/* Success Icon */}
              <View style={[styles.iconContainer, { backgroundColor: theme.colors.primaryContainer }]}>
                <MaterialCommunityIcons 
                  name={icon as any} 
                  size={48} 
                  color={theme.colors.primary} 
                />
              </View>

              {/* Title */}
              <Text 
                variant="headlineSmall" 
                style={[styles.title, { color: theme.colors.onSurface }]}
              >
                {title}
              </Text>

              {/* Message */}
              <Text 
                variant="bodyLarge" 
                style={[styles.message, { color: theme.colors.onSurfaceVariant }]}
              >
                {message}
              </Text>

              {/* Action Buttons */}
              <View style={styles.buttonContainer}>
                {secondaryAction && (
                  <Button
                    mode="outlined"
                    onPress={secondaryAction.onPress}
                    style={[styles.button, styles.secondaryButton]}
                    labelStyle={{ color: theme.colors.primary }}
                  >
                    {secondaryAction.label}
                  </Button>
                )}
                <Button
                  mode="contained"
                  onPress={primaryAction.onPress}
                  style={[styles.button, styles.primaryButton]}
                  buttonColor={theme.colors.primary}
                >
                  {primaryAction.label}
                </Button>
              </View>
            </Card.Content>
          </Card>
        </Animated.View>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  card: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 16,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
  },
  content: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 24,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    textAlign: 'center',
    marginBottom: 12,
    fontWeight: '600',
  },
  message: {
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
  },
  button: {
    flex: 1,
    borderRadius: 12,
  },
  primaryButton: {
    elevation: 2,
  },
  secondaryButton: {
    borderWidth: 1.5,
  },
});
