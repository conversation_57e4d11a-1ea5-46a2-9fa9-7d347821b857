import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Pressable } from 'react-native';
import { Card, Text, IconButton, Chip, useTheme } from 'react-native-paper';
import { Habit, HabitStreak } from '../../types/habit';
import { habitService } from '../../services/habitService';
import { format } from 'date-fns';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring, 
  withSequence,
  runOnJS
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

interface HabitCardProps {
  habit: Habit;
  onPress?: () => void;
  onLongPress?: () => void;
  onCompletionChange?: (habitId: string, completed: boolean) => void;
  showCompletion?: boolean;
  date?: Date;
}

export const HabitCard: React.FC<HabitCardProps> = ({
  habit,
  onPress,
  onLongPress,
  onCompletionChange,
  showCompletion = true,
  date = new Date(),
}) => {
  const theme = useTheme();
  const [isCompleted, setIsCompleted] = useState(false);
  const [completionCount, setCompletionCount] = useState(0);
  const [streak, setStreak] = useState<HabitStreak | null>(null);
  const [loading, setLoading] = useState(false);
  
  const scale = useSharedValue(1);
  const checkScale = useSharedValue(0);

  useEffect(() => {
    loadHabitData();
  }, [habit.id, date]);

  const loadHabitData = async () => {
    try {
      if (habit.frequency === 'multiple_daily') {
        // For multiple daily habits, get completion count
        const completions = await habitService.getHabitCompletions(habit.id, date);
        const count = completions.length;
        setCompletionCount(count);
        setIsCompleted(count >= (habit.dailyTarget || 1));
      } else {
        // For regular habits, check if completed
        const completed = await habitService.isHabitCompleted(habit.id, date);
        setIsCompleted(completed);
        setCompletionCount(completed ? 1 : 0);
      }

      const habitStreak = await habitService.calculateStreak(habit.id);
      setStreak(habitStreak);
    } catch (error) {
      console.error('Failed to load habit data:', error);
    }
  };

  const handleToggleCompletion = async () => {
    if (loading) return;
    
    setLoading(true);
    
    try {
      // Haptic feedback
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      
      if (habit.frequency === 'multiple_daily') {
        const dailyTarget = habit.dailyTarget || 1;

        if (completionCount > 0) {
          // Uncomplete one instance
          await habitService.uncompleteHabit(habit.id, date);
          const newCount = completionCount - 1;
          setCompletionCount(newCount);
          setIsCompleted(newCount >= dailyTarget);

          // Animate check disappearing if fully uncompleted
          if (newCount < dailyTarget) {
            checkScale.value = withSpring(0);
          }
        } else if (completionCount < dailyTarget) {
          // Complete one instance
          await habitService.completeHabit(habit.id, date);
          const newCount = completionCount + 1;
          setCompletionCount(newCount);
          setIsCompleted(newCount >= dailyTarget);

          // Animate check appearing if target reached
          if (newCount >= dailyTarget) {
            checkScale.value = withSequence(
              withSpring(1.2),
              withSpring(1)
            );

            // Card celebration animation
            scale.value = withSequence(
              withSpring(1.05),
              withSpring(1)
            );

            // Success haptic
            runOnJS(Haptics.notificationAsync)(Haptics.NotificationFeedbackType.Success);
          }
        }
      } else {
        // Regular habit logic
        if (isCompleted) {
          await habitService.uncompleteHabit(habit.id, date);
          setIsCompleted(false);
          setCompletionCount(0);

          // Animate check disappearing
          checkScale.value = withSpring(0);
        } else {
          await habitService.completeHabit(habit.id, date);
          setIsCompleted(true);
          setCompletionCount(1);

          // Animate check appearing with celebration
          checkScale.value = withSequence(
            withSpring(1.2),
            withSpring(1)
          );

          // Card celebration animation
          scale.value = withSequence(
            withSpring(1.05),
            withSpring(1)
          );

          // Success haptic
          runOnJS(Haptics.notificationAsync)(Haptics.NotificationFeedbackType.Success);
        }
      }
      
      // Reload streak data
      const updatedStreak = await habitService.calculateStreak(habit.id);
      setStreak(updatedStreak);

      // Notify parent component of completion change
      if (onCompletionChange) {
        onCompletionChange(habit.id, !isCompleted);
      }

    } catch (error) {
      console.error('Failed to toggle habit completion:', error);
    } finally {
      setLoading(false);
    }
  };

  const animatedCardStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const animatedCheckStyle = useAnimatedStyle(() => ({
    transform: [{ scale: checkScale.value }],
  }));

  const cardBackgroundColor = isCompleted 
    ? theme.colors.primaryContainer 
    : theme.colors.surface;

  const borderColor = habit.color;

  return (
    <Animated.View style={animatedCardStyle}>
      <Pressable
        onPress={onPress}
        onLongPress={onLongPress}
        style={({ pressed }) => [
          styles.pressable,
          { opacity: pressed ? 0.8 : 1 }
        ]}
      >
        <Card 
          style={[
            styles.card, 
            { 
              backgroundColor: cardBackgroundColor,
              borderLeftColor: borderColor,
            }
          ]}
          elevation={2}
        >
          <Card.Content style={styles.content}>
            <View style={styles.header}>
              <View style={styles.titleContainer}>
                {habit.emoji && (
                  <Text style={styles.emoji}>{habit.emoji}</Text>
                )}
                <Text 
                  variant="titleMedium" 
                  style={[
                    styles.title,
                    { color: theme.colors.onSurface }
                  ]}
                  numberOfLines={2}
                >
                  {habit.title}
                </Text>
              </View>
              
              {showCompletion && (
                <Pressable
                  onPress={handleToggleCompletion}
                  disabled={loading}
                  style={({ pressed }) => [
                    styles.checkButton,
                    { 
                      backgroundColor: isCompleted 
                        ? theme.colors.primary 
                        : theme.colors.outline,
                      opacity: pressed ? 0.8 : 1
                    }
                  ]}
                >
                  <Animated.View style={animatedCheckStyle}>
                    <IconButton
                      icon={isCompleted ? "check" : "plus"}
                      size={20}
                      iconColor={isCompleted ? theme.colors.onPrimary : theme.colors.onSurface}
                      style={styles.checkIcon}
                    />
                  </Animated.View>
                </Pressable>
              )}
            </View>

            <View style={styles.footer}>
              <View style={styles.frequencyContainer}>
                <Chip
                  mode="flat"
                  compact
                  style={[
                    styles.frequencyChip,
                    {
                      backgroundColor: `${habit.color}20`,
                      borderColor: habit.color,
                      borderWidth: 1.5,
                    }
                  ]}
                  textStyle={{
                    color: habit.color,
                    fontSize: 12,
                    fontWeight: '700',
                    textAlign: 'center',
                    lineHeight: 16,
                  }}
                >
                  {habit.frequency === 'multiple_daily'
                    ? `🎯 ${completionCount}/${habit.dailyTarget || 1} today`
                    : habit.frequency === 'custom' && habit.customDays
                    ? `📅 ${habit.customDays.length}x/week`
                    : `⭐ ${habit.frequency}`
                  }
                </Chip>
              </View>

              {streak && streak.currentStreak > 0 && (
                <View style={[
                  styles.streakContainer,
                  { backgroundColor: `${theme.colors.tertiary}15` }
                ]}>
                  <Text style={{ fontSize: 14, marginRight: 2 }}>
                    {streak.currentStreak >= 30 ? '🏆' :
                     streak.currentStreak >= 14 ? '🔥' :
                     streak.currentStreak >= 7 ? '⚡' : '✨'}
                  </Text>
                  <Text
                    variant="bodySmall"
                    style={[
                      styles.streakText,
                      {
                        color: theme.colors.tertiary,
                        fontWeight: '700',
                        textAlign: 'center',
                        lineHeight: 16,
                      }
                    ]}
                  >
                    {streak.currentStreak} day{streak.currentStreak !== 1 ? 's' : ''}
                  </Text>
                </View>
              )}
            </View>
          </Card.Content>
        </Card>
      </Pressable>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  pressable: {
    marginVertical: 8,
  },
  card: {
    borderLeftWidth: 4,
    borderRadius: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  content: {
    paddingVertical: 18,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  titleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  emoji: {
    fontSize: 32,
    marginRight: 16,
    lineHeight: 36,
  },
  title: {
    flex: 1,
    fontWeight: '600',
    fontSize: 16,
  },
  checkButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 3,
  },
  checkIcon: {
    margin: 0,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  frequencyContainer: {
    flex: 1,
  },
  frequencyChip: {
    alignSelf: 'flex-start',
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 12,
  },
  streakContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    minWidth: 60,
  },
  streakIcon: {
    margin: 0,
    marginRight: -2,
  },
  streakText: {
    fontSize: 12,
    fontWeight: '700',
    textAlign: 'center',
    lineHeight: 16,
  },
});
