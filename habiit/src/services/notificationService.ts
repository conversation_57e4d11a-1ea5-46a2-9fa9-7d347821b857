import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { Habit } from '../types/habit';
import { habitService } from './habitService';
import { storageService } from './storage';
import { STORAGE_KEYS, AppSettings } from '../types/app';

/**
 * Service for managing local notifications for habit reminders
 * Privacy-first approach - all notifications are local only
 */
class NotificationService {
  private isInitialized = false;

  /**
   * Initialize the notification service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Skip notification setup on web for now
      if (Platform.OS === 'web') {
        console.log('Notifications not fully supported on web, skipping initialization');
        this.isInitialized = true;
        return;
      }

      // Configure notification behavior
      Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: false,
        }),
      });

      // Request permissions
      await this.requestPermissions();

      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize notification service:', error);
      // Don't throw error on web, just log it
      if (Platform.OS === 'web') {
        console.log('Continuing without notifications on web');
        this.isInitialized = true;
      } else {
        throw new Error('Notification service initialization failed');
      }
    }
  }

  /**
   * Request notification permissions
   */
  async requestPermissions(): Promise<boolean> {
    try {
      // Skip on web
      if (Platform.OS === 'web') {
        return true;
      }

      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Notification permissions not granted');
        return false;
      }

      // For Android, create notification channel
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('habit-reminders', {
          name: 'Habit Reminders',
          importance: Notifications.AndroidImportance.DEFAULT,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#6366F1',
          sound: 'default',
        });
      }

      return true;
    } catch (error) {
      console.error('Failed to request notification permissions:', error);
      return false;
    }
  }

  /**
   * Check if notifications are enabled in app settings
   */
  async areNotificationsEnabled(): Promise<boolean> {
    try {
      const settings = await storageService.getSecureItem<AppSettings>(STORAGE_KEYS.SETTINGS);
      return settings?.notificationsEnabled ?? true;
    } catch (error) {
      console.error('Failed to check notification settings:', error);
      return false;
    }
  }

  /**
   * Schedule daily reminders for all active habits
   */
  async scheduleAllHabitReminders(): Promise<void> {
    try {
      if (!await this.areNotificationsEnabled()) {
        return;
      }

      // Cancel all existing notifications first
      await this.cancelAllNotifications();

      const habits = await habitService.getActiveHabits();
      const settings = await storageService.getSecureItem<AppSettings>(STORAGE_KEYS.SETTINGS);
      const defaultTime = settings?.defaultReminderTime || '09:00';

      for (const habit of habits) {
        if (habit.reminderEnabled) {
          await this.scheduleHabitReminder(habit, habit.reminderTime || defaultTime);
        }
      }
    } catch (error) {
      console.error('Failed to schedule habit reminders:', error);
    }
  }

  /**
   * Schedule a reminder for a specific habit
   */
  async scheduleHabitReminder(habit: Habit, time: string): Promise<void> {
    try {
      if (!await this.areNotificationsEnabled()) {
        return;
      }

      const [hours, minutes] = time.split(':').map(Number);
      
      // Create trigger based on habit frequency
      let trigger: Notifications.NotificationTriggerInput;

      if (habit.frequency === 'daily') {
        trigger = {
          hour: hours,
          minute: minutes,
          repeats: true,
        };
      } else if (habit.frequency === 'weekly') {
        // Schedule for the same day each week (default to Monday)
        trigger = {
          weekday: 2, // Monday
          hour: hours,
          minute: minutes,
          repeats: true,
        };
      } else if (habit.frequency === 'custom' && habit.customDays) {
        // Schedule for each custom day
        for (const dayIndex of habit.customDays) {
          const weekday = dayIndex === 0 ? 1 : dayIndex + 1; // Convert Sunday=0 to Sunday=1
          
          await Notifications.scheduleNotificationAsync({
            identifier: `${habit.id}-${dayIndex}`,
            content: {
              title: '🎯 Habit Reminder',
              body: `Time to ${habit.title} ${habit.emoji || ''}`,
              data: {
                habitId: habit.id,
                type: 'habit-reminder',
              },
              sound: 'default',
            },
            trigger: {
              weekday,
              hour: hours,
              minute: minutes,
              repeats: true,
            },
          });
        }
        return; // Early return for custom days
      } else {
        return; // Skip if frequency is not supported
      }

      // Schedule the notification for daily/weekly
      await Notifications.scheduleNotificationAsync({
        identifier: habit.id,
        content: {
          title: '🎯 Habit Reminder',
          body: `Time to ${habit.title} ${habit.emoji || ''}`,
          data: {
            habitId: habit.id,
            type: 'habit-reminder',
          },
          sound: 'default',
        },
        trigger,
      });
    } catch (error) {
      console.error(`Failed to schedule reminder for habit ${habit.id}:`, error);
    }
  }

  /**
   * Cancel a specific habit's reminders
   */
  async cancelHabitReminders(habitId: string): Promise<void> {
    try {
      // Cancel main notification
      await Notifications.cancelScheduledNotificationAsync(habitId);
      
      // Cancel custom day notifications (0-6 for each day)
      for (let i = 0; i < 7; i++) {
        await Notifications.cancelScheduledNotificationAsync(`${habitId}-${i}`);
      }
    } catch (error) {
      console.error(`Failed to cancel reminders for habit ${habitId}:`, error);
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Failed to cancel all notifications:', error);
    }
  }

  /**
   * Send an immediate notification (for testing or special events)
   */
  async sendImmediateNotification(title: string, body: string, data?: any): Promise<void> {
    try {
      if (!await this.areNotificationsEnabled()) {
        return;
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: data || {},
          sound: 'default',
        },
        trigger: null, // Send immediately
      });
    } catch (error) {
      console.error('Failed to send immediate notification:', error);
    }
  }

  /**
   * Get all scheduled notifications (for debugging)
   */
  async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Failed to get scheduled notifications:', error);
      return [];
    }
  }

  /**
   * Handle notification received while app is in foreground
   */
  addNotificationReceivedListener(listener: (notification: Notifications.Notification) => void): Notifications.Subscription {
    return Notifications.addNotificationReceivedListener(listener);
  }

  /**
   * Handle notification response (when user taps notification)
   */
  addNotificationResponseReceivedListener(listener: (response: Notifications.NotificationResponse) => void): Notifications.Subscription {
    return Notifications.addNotificationResponseReceivedListener(listener);
  }

  /**
   * Send a celebration notification when user completes a streak milestone
   */
  async sendStreakCelebration(habitTitle: string, streakCount: number): Promise<void> {
    try {
      let title = '🎉 Great job!';
      let body = `You've completed ${habitTitle} for ${streakCount} days in a row!`;

      // Special messages for milestone streaks
      if (streakCount === 7) {
        title = '🔥 One week streak!';
        body = `Amazing! You've kept up with ${habitTitle} for a whole week!`;
      } else if (streakCount === 30) {
        title = '🏆 30-day streak!';
        body = `Incredible! You've maintained ${habitTitle} for a full month!`;
      } else if (streakCount === 100) {
        title = '💎 100-day streak!';
        body = `Legendary! ${habitTitle} has become a true habit - 100 days strong!`;
      } else if (streakCount % 10 === 0 && streakCount >= 10) {
        title = `🚀 ${streakCount}-day streak!`;
        body = `Keep it up! You're crushing ${habitTitle}!`;
      }

      await this.sendImmediateNotification(title, body, {
        type: 'streak-celebration',
        streakCount,
        habitTitle,
      });
    } catch (error) {
      console.error('Failed to send streak celebration:', error);
    }
  }

  /**
   * Send daily motivation notification
   */
  async sendDailyMotivation(): Promise<void> {
    try {
      const motivationalMessages = [
        "🌟 Every small step counts towards your goals!",
        "💪 You've got this! Start your day with purpose.",
        "🎯 Consistency is the key to building lasting habits.",
        "🌱 Growth happens one day at a time.",
        "✨ Today is a new opportunity to be your best self.",
        "🔥 Your future self will thank you for today's efforts.",
        "🏃‍♂️ Progress, not perfection, is what matters.",
        "🌈 Small habits, big changes!",
      ];

      const randomMessage = motivationalMessages[Math.floor(Math.random() * motivationalMessages.length)];
      
      await this.sendImmediateNotification(
        'Good morning!',
        randomMessage,
        { type: 'daily-motivation' }
      );
    } catch (error) {
      console.error('Failed to send daily motivation:', error);
    }
  }
}

export const notificationService = new NotificationService();
