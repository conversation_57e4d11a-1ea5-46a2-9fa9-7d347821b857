import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Crypto from 'expo-crypto';
import { Platform } from 'react-native';
import { STORAGE_KEYS, StorageKey } from '../types/app';

/**
 * Privacy-first storage service that uses encryption for sensitive data
 * All user data is stored locally on device with no cloud sync by default
 */
class StorageService {
  private encryptionKey: string | null = null;

  /**
   * Initialize the storage service and generate/retrieve encryption key
   */
  async initialize(): Promise<void> {
    try {
      // Try to get existing encryption key
      if (Platform.OS === 'web') {
        this.encryptionKey = await AsyncStorage.getItem('encryption_key');
      } else {
        this.encryptionKey = await SecureStore.getItemAsync('encryption_key');
      }

      if (!this.encryptionKey) {
        // Generate new encryption key if none exists
        this.encryptionKey = await this.generateEncryptionKey();

        if (Platform.OS === 'web') {
          await AsyncStorage.setItem('encryption_key', this.encryptionKey);
        } else {
          await SecureStore.setItemAsync('encryption_key', this.encryptionKey);
        }
      }
    } catch (error) {
      console.error('Failed to initialize storage service:', error);
      throw new Error('Storage initialization failed');
    }
  }

  /**
   * Generate a secure encryption key
   */
  private async generateEncryptionKey(): Promise<string> {
    const randomBytes = await Crypto.getRandomBytesAsync(32);
    return Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Encrypt data using simple encoding (for demo purposes)
   */
  private async encryptData(data: string): Promise<string> {
    if (!this.encryptionKey) {
      throw new Error('Storage service not initialized');
    }

    try {
      // For simplicity, we'll use base64 encoding with the key
      // In production, you'd want to use proper AES encryption
      const combined = this.encryptionKey + data;

      // Use btoa for web compatibility
      if (Platform.OS === 'web') {
        return btoa(combined);
      } else {
        return Buffer.from(combined).toString('base64');
      }
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Data encryption failed');
    }
  }

  /**
   * Decrypt data
   */
  private async decryptData(encryptedData: string): Promise<string> {
    if (!this.encryptionKey) {
      throw new Error('Storage service not initialized');
    }

    try {
      let decoded: string;

      // Use atob for web compatibility
      if (Platform.OS === 'web') {
        decoded = atob(encryptedData);
      } else {
        decoded = Buffer.from(encryptedData, 'base64').toString();
      }

      const data = decoded.substring(this.encryptionKey.length);
      return data;
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Data decryption failed');
    }
  }

  /**
   * Store encrypted data securely
   */
  async setSecureItem(key: StorageKey, value: any): Promise<void> {
    try {
      const jsonString = JSON.stringify(value);
      const encryptedData = await this.encryptData(jsonString);
      
      if (Platform.OS === 'web') {
        // Use AsyncStorage for web platform
        await AsyncStorage.setItem(key, encryptedData);
      } else {
        // Use SecureStore for native platforms
        await SecureStore.setItemAsync(key, encryptedData);
      }
    } catch (error) {
      console.error(`Failed to store secure item ${key}:`, error);
      throw new Error(`Storage operation failed for ${key}`);
    }
  }

  /**
   * Retrieve and decrypt data
   */
  async getSecureItem<T>(key: StorageKey): Promise<T | null> {
    try {
      let encryptedData: string | null;
      
      if (Platform.OS === 'web') {
        encryptedData = await AsyncStorage.getItem(key);
      } else {
        encryptedData = await SecureStore.getItemAsync(key);
      }

      if (!encryptedData) {
        return null;
      }

      const decryptedData = await this.decryptData(encryptedData);
      return JSON.parse(decryptedData) as T;
    } catch (error) {
      console.error(`Failed to retrieve secure item ${key}:`, error);
      return null;
    }
  }

  /**
   * Remove encrypted data
   */
  async removeSecureItem(key: StorageKey): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        await AsyncStorage.removeItem(key);
      } else {
        await SecureStore.deleteItemAsync(key);
      }
    } catch (error) {
      console.error(`Failed to remove secure item ${key}:`, error);
      throw new Error(`Remove operation failed for ${key}`);
    }
  }

  /**
   * Clear all app data (for privacy/reset functionality)
   */
  async clearAllData(): Promise<void> {
    try {
      const keys = Object.values(STORAGE_KEYS);
      await Promise.all(keys.map(key => this.removeSecureItem(key)));

      // Also remove the encryption key to force regeneration
      if (Platform.OS === 'web') {
        await AsyncStorage.removeItem('encryption_key');
      } else {
        await SecureStore.deleteItemAsync('encryption_key');
      }

      this.encryptionKey = null;
    } catch (error) {
      console.error('Failed to clear all data:', error);
      throw new Error('Data clearing failed');
    }
  }

  /**
   * Export all data for backup (encrypted)
   */
  async exportData(): Promise<string> {
    try {
      const data: Record<string, any> = {};
      const keys = Object.values(STORAGE_KEYS);
      
      for (const key of keys) {
        const value = await this.getSecureItem(key);
        if (value !== null) {
          data[key] = value;
        }
      }

      return JSON.stringify(data, null, 2);
    } catch (error) {
      console.error('Failed to export data:', error);
      throw new Error('Data export failed');
    }
  }
}

// Singleton instance
export const storageService = new StorageService();
