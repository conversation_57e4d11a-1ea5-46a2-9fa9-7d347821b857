import { 
  Habit, 
  HabitCompletion, 
  HabitStreak, 
  HabitStatistics, 
  HabitFormData,
  MonthlyHabitData 
} from '../types/habit';
import { STORAGE_KEYS } from '../types/app';
import { storageService } from './storage';
import { format, parseISO, startOfDay, endOfDay, differenceInDays, subDays, startOfMonth, endOfMonth } from 'date-fns';

/**
 * Service for managing habits and their completions
 * Handles CRUD operations, streak tracking, and statistics
 */
class HabitService {
  /**
   * Create a new habit
   */
  async createHabit(formData: HabitFormData): Promise<Habit> {
    const habits = await this.getAllHabits();
    
    const newHabit: Habit = {
      id: this.generateId(),
      title: formData.title,
      emoji: formData.emoji,
      color: formData.color,
      frequency: formData.frequency,
      customDays: formData.customDays,
      reminderTime: formData.reminderTime,
      reminderEnabled: formData.reminderEnabled,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
      // Multiple daily fields
      dailyTarget: formData.dailyTarget,
      reminderInterval: formData.reminderInterval,
      reminderTimes: formData.reminderTimes,
    };

    habits.push(newHabit);
    await storageService.setSecureItem(STORAGE_KEYS.HABITS, habits);

    // Schedule notifications if enabled
    if (newHabit.reminderEnabled) {
      try {
        const { notificationService } = await import('./notificationService');
        await notificationService.scheduleHabitReminder(newHabit, newHabit.reminderTime || '09:00');
      } catch (error) {
        console.error('Failed to schedule notification for new habit:', error);
      }
    }

    return newHabit;
  }

  /**
   * Update an existing habit
   */
  async updateHabit(habitId: string, formData: Partial<HabitFormData>): Promise<Habit> {
    const habits = await this.getAllHabits();
    const habitIndex = habits.findIndex(h => h.id === habitId);
    
    if (habitIndex === -1) {
      throw new Error('Habit not found');
    }

    const updatedHabit: Habit = {
      ...habits[habitIndex],
      ...formData,
      updatedAt: new Date().toISOString(),
    };

    habits[habitIndex] = updatedHabit;
    await storageService.setSecureItem(STORAGE_KEYS.HABITS, habits);

    // Update notifications
    try {
      const { notificationService } = await import('./notificationService');
      await notificationService.cancelHabitReminders(habitId);

      if (updatedHabit.reminderEnabled) {
        await notificationService.scheduleHabitReminder(updatedHabit, updatedHabit.reminderTime || '09:00');
      }
    } catch (error) {
      console.error('Failed to update notifications for habit:', error);
    }

    return updatedHabit;
  }

  /**
   * Delete a habit and all its completions
   */
  async deleteHabit(habitId: string): Promise<void> {
    const habits = await this.getAllHabits();
    const filteredHabits = habits.filter(h => h.id !== habitId);
    
    await storageService.setSecureItem(STORAGE_KEYS.HABITS, filteredHabits);

    // Also remove all completions for this habit
    const completions = await this.getAllCompletions();
    const filteredCompletions = completions.filter(c => c.habitId !== habitId);
    await storageService.setSecureItem(STORAGE_KEYS.COMPLETIONS, filteredCompletions);

    // Cancel notifications for this habit
    try {
      const { notificationService } = await import('./notificationService');
      await notificationService.cancelHabitReminders(habitId);
    } catch (error) {
      console.error('Failed to cancel notifications for deleted habit:', error);
    }
  }

  /**
   * Get all habits
   */
  async getAllHabits(): Promise<Habit[]> {
    const habits = await storageService.getSecureItem<Habit[]>(STORAGE_KEYS.HABITS);
    return habits || [];
  }

  /**
   * Get active habits only
   */
  async getActiveHabits(): Promise<Habit[]> {
    const habits = await this.getAllHabits();
    return habits.filter(h => h.isActive);
  }

  /**
   * Get a specific habit by ID
   */
  async getHabitById(habitId: string): Promise<Habit | null> {
    const habits = await this.getAllHabits();
    return habits.find(h => h.id === habitId) || null;
  }

  /**
   * Mark a habit as completed for a specific date
   */
  async completeHabit(habitId: string, date: Date = new Date()): Promise<HabitCompletion> {
    const habit = await this.getHabitById(habitId);
    if (!habit) {
      throw new Error('Habit not found');
    }

    const dateString = format(date, 'yyyy-MM-dd');
    const completions = await this.getAllCompletions();

    // For multiple daily habits, find the next completion index
    if (habit.frequency === 'multiple_daily') {
      const todayCompletions = completions.filter(
        c => c.habitId === habitId && c.date === dateString
      );

      const dailyTarget = habit.dailyTarget || 1;
      if (todayCompletions.length >= dailyTarget) {
        // Already completed all required times for today
        return todayCompletions[todayCompletions.length - 1];
      }

      const newCompletion: HabitCompletion = {
        id: this.generateId(),
        habitId,
        date: dateString,
        completedAt: new Date().toISOString(),
        completionIndex: todayCompletions.length,
      };

      completions.push(newCompletion);
      await storageService.setSecureItem(STORAGE_KEYS.COMPLETIONS, completions);

      // Check for daily target completion celebration
      if (todayCompletions.length + 1 === dailyTarget) {
        try {
          const { notificationService } = await import('./notificationService');
          await notificationService.sendDailyTargetCelebration(habit.title, dailyTarget);
        } catch (error) {
          console.error('Failed to send daily target celebration:', error);
        }
      }

      return newCompletion;
    } else {
      // Regular single completion per day logic
      const existingCompletion = completions.find(
        c => c.habitId === habitId && c.date === dateString
      );

      if (existingCompletion) {
        return existingCompletion;
      }

      const newCompletion: HabitCompletion = {
        id: this.generateId(),
        habitId,
        date: dateString,
        completedAt: new Date().toISOString(),
        completionIndex: 0,
      };

      completions.push(newCompletion);
      await storageService.setSecureItem(STORAGE_KEYS.COMPLETIONS, completions);

      // Check for streak milestones and send celebration
      try {
        const streak = await this.calculateStreak(habitId);

        if (habit && streak.currentStreak > 0 && (
          streak.currentStreak === 7 ||
          streak.currentStreak === 30 ||
          streak.currentStreak === 100 ||
          (streak.currentStreak % 10 === 0 && streak.currentStreak >= 10)
        )) {
          const { notificationService } = await import('./notificationService');
          await notificationService.sendStreakCelebration(habit.title, streak.currentStreak);
        }
      } catch (error) {
        console.error('Failed to send streak celebration:', error);
      }

      return newCompletion;
    }
  }

  /**
   * Get today's completion count for a habit (useful for multiple daily habits)
   */
  async getTodayCompletionCount(habitId: string, date: Date = new Date()): Promise<number> {
    const dateString = format(date, 'yyyy-MM-dd');
    const completions = await this.getAllCompletions();

    return completions.filter(
      c => c.habitId === habitId && c.date === dateString
    ).length;
  }

  /**
   * Remove a habit completion (removes the latest completion for multiple daily habits)
   */
  async uncompleteHabit(habitId: string, date: Date = new Date()): Promise<void> {
    const habit = await this.getHabitById(habitId);
    if (!habit) {
      throw new Error('Habit not found');
    }

    const dateString = format(date, 'yyyy-MM-dd');
    const completions = await this.getAllCompletions();

    if (habit.frequency === 'multiple_daily') {
      // For multiple daily habits, remove the latest completion for today
      const todayCompletions = completions.filter(
        c => c.habitId === habitId && c.date === dateString
      );

      if (todayCompletions.length === 0) {
        return; // Nothing to remove
      }

      // Find the latest completion (highest completionIndex)
      const latestCompletion = todayCompletions.reduce((latest, current) =>
        (current.completionIndex || 0) > (latest.completionIndex || 0) ? current : latest
      );

      const filteredCompletions = completions.filter(c => c.id !== latestCompletion.id);
      await storageService.setSecureItem(STORAGE_KEYS.COMPLETIONS, filteredCompletions);
    } else {
      // For regular habits, remove all completions for the date
      const filteredCompletions = completions.filter(
        c => !(c.habitId === habitId && c.date === dateString)
      );

      await storageService.setSecureItem(STORAGE_KEYS.COMPLETIONS, filteredCompletions);
    }
  }

  /**
   * Check if a habit is completed for a specific date
   */
  async isHabitCompleted(habitId: string, date: Date = new Date()): Promise<boolean> {
    const dateString = format(date, 'yyyy-MM-dd');
    const completions = await this.getAllCompletions();
    
    return completions.some(c => c.habitId === habitId && c.date === dateString);
  }

  /**
   * Get all completions
   */
  async getAllCompletions(): Promise<HabitCompletion[]> {
    const completions = await storageService.getSecureItem<HabitCompletion[]>(STORAGE_KEYS.COMPLETIONS);
    return completions || [];
  }

  /**
   * Get completions for a specific habit
   */
  async getHabitCompletions(habitId: string): Promise<HabitCompletion[]> {
    const completions = await this.getAllCompletions();
    return completions.filter(c => c.habitId === habitId);
  }

  /**
   * Calculate streak for a habit
   */
  async calculateStreak(habitId: string): Promise<HabitStreak> {
    const completions = await this.getHabitCompletions(habitId);
    const habit = await this.getHabitById(habitId);
    
    if (!habit) {
      throw new Error('Habit not found');
    }

    // Sort completions by date (newest first)
    const sortedCompletions = completions
      .sort((a, b) => b.date.localeCompare(a.date))
      .map(c => c.date);

    let currentStreak = 0;
    let longestStreak = 0;
    let tempStreak = 0;
    let lastCompletedDate: string | undefined;

    if (sortedCompletions.length === 0) {
      return {
        habitId,
        currentStreak: 0,
        longestStreak: 0,
      };
    }

    lastCompletedDate = sortedCompletions[0];
    const today = format(new Date(), 'yyyy-MM-dd');
    const yesterday = format(subDays(new Date(), 1), 'yyyy-MM-dd');

    // Calculate current streak
    if (sortedCompletions.includes(today) || sortedCompletions.includes(yesterday)) {
      let checkDate = sortedCompletions.includes(today) ? today : yesterday;
      
      while (sortedCompletions.includes(checkDate)) {
        currentStreak++;
        checkDate = format(subDays(parseISO(checkDate), 1), 'yyyy-MM-dd');
      }
    }

    // Calculate longest streak
    for (let i = 0; i < sortedCompletions.length; i++) {
      const currentDate = sortedCompletions[i];
      const nextDate = sortedCompletions[i + 1];
      
      tempStreak = 1;
      
      if (nextDate) {
        const daysDiff = differenceInDays(parseISO(currentDate), parseISO(nextDate));
        if (daysDiff === 1) {
          // Continue counting consecutive days
          let j = i + 1;
          while (j < sortedCompletions.length) {
            const diff = differenceInDays(parseISO(sortedCompletions[j - 1]), parseISO(sortedCompletions[j]));
            if (diff === 1) {
              tempStreak++;
              j++;
            } else {
              break;
            }
          }
          i = j - 1; // Skip the counted days
        }
      }
      
      longestStreak = Math.max(longestStreak, tempStreak);
    }

    return {
      habitId,
      currentStreak,
      longestStreak,
      lastCompletedDate,
    };
  }

  /**
   * Get habit statistics
   */
  async getHabitStatistics(habitId: string): Promise<HabitStatistics> {
    const habit = await this.getHabitById(habitId);
    if (!habit) {
      throw new Error('Habit not found');
    }

    const completions = await this.getHabitCompletions(habitId);
    const streak = await this.calculateStreak(habitId);

    // Calculate total completions
    const totalCompletions = completions.length;

    // Calculate completion rate (last 30 days)
    const thirtyDaysAgo = subDays(new Date(), 30);
    const recentCompletions = completions.filter(c =>
      parseISO(c.date) >= thirtyDaysAgo
    );

    let expectedCompletions = 30; // Default for daily habits
    if (habit.frequency === 'weekly') {
      expectedCompletions = 4; // ~4 weeks in 30 days
    } else if (habit.frequency === 'custom' && habit.customDays) {
      // Calculate based on custom days
      expectedCompletions = Math.floor(30 * habit.customDays.length / 7);
    }

    const completionRate = expectedCompletions > 0 ? recentCompletions.length / expectedCompletions : 0;

    // Calculate average weekly completions
    const weeksActive = Math.max(1, Math.ceil(differenceInDays(new Date(), parseISO(habit.createdAt)) / 7));
    const averageWeeklyCompletions = totalCompletions / weeksActive;

    // Calculate monthly data
    const monthlyData = await this.getMonthlyData(habitId, habit);

    return {
      habitId,
      totalCompletions,
      completionRate: Math.min(1, completionRate),
      averageWeeklyCompletions,
      streak,
      monthlyData,
    };
  }

  /**
   * Get monthly completion data for a habit
   */
  private async getMonthlyData(habitId: string, habit: Habit): Promise<MonthlyHabitData[]> {
    const completions = await this.getHabitCompletions(habitId);
    const monthlyData: MonthlyHabitData[] = [];

    // Get data for last 12 months
    for (let i = 11; i >= 0; i--) {
      const date = subDays(new Date(), i * 30);
      const monthStart = startOfMonth(date);
      const monthEnd = endOfMonth(date);
      const monthKey = format(monthStart, 'yyyy-MM');

      const monthCompletions = completions.filter(c => {
        const completionDate = parseISO(c.date);
        return completionDate >= monthStart && completionDate <= monthEnd;
      });

      // Calculate expected completions for this month
      let expectedCompletions = 30; // Default for daily
      if (habit.frequency === 'weekly') {
        expectedCompletions = 4;
      } else if (habit.frequency === 'custom' && habit.customDays) {
        expectedCompletions = Math.floor(30 * habit.customDays.length / 7);
      }

      const completionRate = expectedCompletions > 0 ? monthCompletions.length / expectedCompletions : 0;

      monthlyData.push({
        month: monthKey,
        completions: monthCompletions.length,
        expectedCompletions,
        completionRate: Math.min(1, completionRate),
      });
    }

    return monthlyData;
  }

  /**
   * Generate a unique ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

export const habitService = new HabitService();
