import '@testing-library/jest-native/extend-expect';

// Mock react-native-reanimated
jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');

  // The mock for `call` immediately calls the callback which is incorrect
  // So we override it with a no-op
  Reanimated.default.call = () => {};

  return Reanimated;
});

// Mock react-native-gesture-handler
jest.mock('react-native-gesture-handler', () => {
  const View = require('react-native/Libraries/Components/View/View');
  return {
    Swipeable: View,
    DrawerLayout: View,
    State: {},
    ScrollView: View,
    Slider: View,
    Switch: View,
    TextInput: View,
    ToolbarAndroid: View,
    ViewPagerAndroid: View,
    DrawerLayoutAndroid: View,
    WebView: View,
    NativeViewGestureHandler: View,
    TapGestureHandler: View,
    FlingGestureHandler: View,
    ForceTouchGestureHandler: View,
    LongPressGestureHandler: View,
    PanGestureHandler: View,
    PinchGestureHandler: View,
    RotationGestureHandler: View,
    RawButton: View,
    BaseButton: View,
    RectButton: View,
    BorderlessButton: View,
    FlatList: View,
    gestureHandlerRootHOC: jest.fn(),
    Directions: {},
  };
});

// Mock expo modules
jest.mock('expo-secure-store', () => ({
  getItemAsync: jest.fn(),
  setItemAsync: jest.fn(),
  deleteItemAsync: jest.fn(),
}));

jest.mock('expo-notifications', () => ({
  setNotificationHandler: jest.fn(),
  getPermissionsAsync: jest.fn(),
  requestPermissionsAsync: jest.fn(),
  setNotificationChannelAsync: jest.fn(),
  scheduleNotificationAsync: jest.fn(),
  cancelScheduledNotificationAsync: jest.fn(),
  cancelAllScheduledNotificationsAsync: jest.fn(),
  getAllScheduledNotificationsAsync: jest.fn(),
  addNotificationReceivedListener: jest.fn(),
  addNotificationResponseReceivedListener: jest.fn(),
  AndroidImportance: {
    DEFAULT: 'default',
  },
}));

jest.mock('expo-crypto', () => ({
  getRandomBytesAsync: jest.fn(),
}));

jest.mock('expo-localization', () => ({
  getLocales: jest.fn(() => [{ languageCode: 'en', regionCode: 'US' }]),
}));

jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(),
  notificationAsync: jest.fn(),
  ImpactFeedbackStyle: {
    Light: 'light',
    Medium: 'medium',
    Heavy: 'heavy',
  },
  NotificationFeedbackType: {
    Success: 'success',
    Warning: 'warning',
    Error: 'error',
  },
}));

// Mock @react-native-async-storage/async-storage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  getAllKeys: jest.fn(),
  multiGet: jest.fn(),
  multiSet: jest.fn(),
  multiRemove: jest.fn(),
}));

// Mock react-native-paper
jest.mock('react-native-paper', () => {
  const RN = require('react-native');
  
  return {
    Provider: ({ children }: any) => children,
    DefaultTheme: {},
    DarkTheme: {},
    useTheme: () => ({
      colors: {
        primary: '#6366F1',
        surface: '#FFFFFF',
        background: '#FAFAFA',
        onSurface: '#1F2937',
        onBackground: '#1F2937',
        onSurfaceVariant: '#6B7280',
        outline: '#D1D5DB',
        error: '#EF4444',
        onPrimary: '#FFFFFF',
        secondary: '#10B981',
        tertiary: '#F59E0B',
        primaryContainer: '#E0E7FF',
        onPrimaryContainer: '#1E1B4B',
        surfaceVariant: '#F8FAFC',
      },
    }),
    Text: RN.Text,
    Button: RN.TouchableOpacity,
    Card: RN.View,
    List: {
      Item: RN.View,
      Section: RN.View,
      Subheader: RN.Text,
      Icon: RN.View,
    },
    TextInput: RN.TextInput,
    Switch: RN.Switch,
    Chip: RN.TouchableOpacity,
    FAB: RN.TouchableOpacity,
    Appbar: {
      Header: RN.View,
      Content: RN.View,
      Action: RN.TouchableOpacity,
    },
    Divider: RN.View,
    IconButton: RN.TouchableOpacity,
    Snackbar: RN.View,
    Dialog: RN.View,
    Portal: ({ children }: any) => children,
    ActivityIndicator: RN.ActivityIndicator,
    configureFonts: jest.fn(),
    MD3LightTheme: {},
    MD3DarkTheme: {},
    PaperProvider: ({ children }: any) => children,
  };
});

// Mock @expo/vector-icons
jest.mock('@expo/vector-icons', () => ({
  MaterialCommunityIcons: 'MaterialCommunityIcons',
}));

// Mock expo-router
jest.mock('expo-router', () => ({
  router: {
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
  },
  useLocalSearchParams: jest.fn(() => ({})),
  useFocusEffect: jest.fn((callback) => callback()),
  Stack: {
    Screen: ({ children }: any) => children,
  },
  Tabs: {
    Screen: ({ children }: any) => children,
  },
}));

// Mock react-native-safe-area-context
jest.mock('react-native-safe-area-context', () => ({
  SafeAreaView: require('react-native').View,
  SafeAreaProvider: ({ children }: any) => children,
  useSafeAreaInsets: () => ({ top: 0, bottom: 0, left: 0, right: 0 }),
}));

// Mock date-fns
jest.mock('date-fns', () => ({
  format: jest.fn((date, formatStr) => {
    if (formatStr === 'yyyy-MM-dd') {
      return date.toISOString().split('T')[0];
    }
    if (formatStr === 'MMMM d, yyyy') {
      return date.toLocaleDateString();
    }
    return date.toString();
  }),
  parseISO: jest.fn((dateString) => new Date(dateString)),
  isToday: jest.fn(() => false),
  startOfDay: jest.fn((date) => date),
  endOfDay: jest.fn((date) => date),
  differenceInDays: jest.fn(() => 1),
  subDays: jest.fn((date, days) => new Date(date.getTime() - days * 24 * 60 * 60 * 1000)),
  startOfMonth: jest.fn((date) => date),
  endOfMonth: jest.fn((date) => date),
}));

// Silence console warnings during tests
const originalWarn = console.warn;
const originalError = console.error;

beforeAll(() => {
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.warn = originalWarn;
  console.error = originalError;
});

// Global test timeout
jest.setTimeout(10000);
