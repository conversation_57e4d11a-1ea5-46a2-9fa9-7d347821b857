import { habitService } from '../services/habitService';
import { storageService } from '../services/storage';
import { HabitFormData, Habit, HabitCompletion } from '../types/habit';

// Mock the storage service
jest.mock('../services/storage');
const mockStorageService = storageService as jest.Mocked<typeof storageService>;

// Mock date-fns to have consistent dates in tests
jest.mock('date-fns', () => ({
  ...jest.requireActual('date-fns'),
  format: jest.fn((date: Date, formatStr: string) => {
    if (formatStr === 'yyyy-MM-dd') {
      return '2024-01-15';
    }
    return date.toISOString();
  }),
}));

describe('HabitService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockStorageService.getSecureItem.mockResolvedValue([]);
    mockStorageService.setSecureItem.mockResolvedValue();
  });

  describe('createHabit', () => {
    it('should create a daily habit successfully', async () => {
      const formData: HabitFormData = {
        title: 'Drink Water',
        emoji: '💧',
        color: '#4ECDC4',
        frequency: 'daily',
        reminderTime: '09:00',
        reminderEnabled: true,
      };

      const habit = await habitService.createHabit(formData);

      expect(habit).toMatchObject({
        title: 'Drink Water',
        emoji: '💧',
        color: '#4ECDC4',
        frequency: 'daily',
        reminderTime: '09:00',
        reminderEnabled: true,
        isActive: true,
      });
      expect(habit.id).toBeDefined();
      expect(habit.createdAt).toBeDefined();
      expect(habit.updatedAt).toBeDefined();
    });

    it('should create a weekly habit successfully', async () => {
      const formData: HabitFormData = {
        title: 'Go to Gym',
        emoji: '💪',
        color: '#FF6B6B',
        frequency: 'weekly',
        reminderTime: '18:00',
        reminderEnabled: false,
      };

      const habit = await habitService.createHabit(formData);

      expect(habit).toMatchObject({
        title: 'Go to Gym',
        emoji: '💪',
        color: '#FF6B6B',
        frequency: 'weekly',
        reminderTime: '18:00',
        reminderEnabled: false,
      });
    });

    it('should create a custom frequency habit successfully', async () => {
      const formData: HabitFormData = {
        title: 'Read Book',
        emoji: '📚',
        color: '#45B7D1',
        frequency: 'custom',
        customDays: [1, 3, 5], // Monday, Wednesday, Friday
        reminderTime: '20:00',
        reminderEnabled: true,
      };

      const habit = await habitService.createHabit(formData);

      expect(habit).toMatchObject({
        title: 'Read Book',
        emoji: '📚',
        color: '#45B7D1',
        frequency: 'custom',
        customDays: [1, 3, 5],
        reminderTime: '20:00',
        reminderEnabled: true,
      });
    });

    it('should create a multiple daily habit successfully', async () => {
      const formData: HabitFormData = {
        title: 'Drink Water',
        emoji: '💧',
        color: '#4ECDC4',
        frequency: 'multiple_daily',
        dailyTarget: 8,
        reminderInterval: 60,
        reminderEnabled: true,
      };

      const habit = await habitService.createHabit(formData);

      expect(habit).toMatchObject({
        title: 'Drink Water',
        emoji: '💧',
        color: '#4ECDC4',
        frequency: 'multiple_daily',
        dailyTarget: 8,
        reminderInterval: 60,
        reminderEnabled: true,
      });
    });

    it('should create a multiple daily habit with specific times', async () => {
      const formData: HabitFormData = {
        title: 'Take Medication',
        emoji: '💊',
        color: '#96CEB4',
        frequency: 'multiple_daily',
        dailyTarget: 3,
        reminderTimes: ['08:00', '14:00', '20:00'],
        reminderEnabled: true,
      };

      const habit = await habitService.createHabit(formData);

      expect(habit).toMatchObject({
        title: 'Take Medication',
        emoji: '💊',
        color: '#96CEB4',
        frequency: 'multiple_daily',
        dailyTarget: 3,
        reminderTimes: ['08:00', '14:00', '20:00'],
        reminderEnabled: true,
      });
    });
  });

  describe('completeHabit', () => {
    const mockHabit: Habit = {
      id: 'habit-1',
      title: 'Test Habit',
      color: '#4ECDC4',
      frequency: 'daily',
      reminderEnabled: false,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      isActive: true,
    };

    beforeEach(() => {
      mockStorageService.getSecureItem.mockImplementation((key) => {
        if (key === 'habits') {
          return Promise.resolve([mockHabit]);
        }
        if (key === 'completions') {
          return Promise.resolve([]);
        }
        return Promise.resolve([]);
      });
    });

    it('should complete a daily habit successfully', async () => {
      const completion = await habitService.completeHabit('habit-1');

      expect(completion).toMatchObject({
        habitId: 'habit-1',
        date: '2024-01-15',
        completionIndex: 0,
      });
      expect(completion.id).toBeDefined();
      expect(completion.completedAt).toBeDefined();
    });

    it('should not create duplicate completions for daily habits', async () => {
      const existingCompletion: HabitCompletion = {
        id: 'completion-1',
        habitId: 'habit-1',
        date: '2024-01-15',
        completedAt: '2024-01-15T10:00:00.000Z',
        completionIndex: 0,
      };

      mockStorageService.getSecureItem.mockImplementation((key) => {
        if (key === 'habits') {
          return Promise.resolve([mockHabit]);
        }
        if (key === 'completions') {
          return Promise.resolve([existingCompletion]);
        }
        return Promise.resolve([]);
      });

      const completion = await habitService.completeHabit('habit-1');

      expect(completion).toEqual(existingCompletion);
      expect(mockStorageService.setSecureItem).not.toHaveBeenCalled();
    });
  });

  describe('completeHabit - Multiple Daily', () => {
    const mockMultipleDailyHabit: Habit = {
      id: 'habit-2',
      title: 'Drink Water',
      color: '#4ECDC4',
      frequency: 'multiple_daily',
      dailyTarget: 3,
      reminderEnabled: false,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      isActive: true,
    };

    beforeEach(() => {
      mockStorageService.getSecureItem.mockImplementation((key) => {
        if (key === 'habits') {
          return Promise.resolve([mockMultipleDailyHabit]);
        }
        if (key === 'completions') {
          return Promise.resolve([]);
        }
        return Promise.resolve([]);
      });
    });

    it('should complete multiple daily habit first time', async () => {
      const completion = await habitService.completeHabit('habit-2');

      expect(completion).toMatchObject({
        habitId: 'habit-2',
        date: '2024-01-15',
        completionIndex: 0,
      });
    });

    it('should complete multiple daily habit second time', async () => {
      const existingCompletion: HabitCompletion = {
        id: 'completion-1',
        habitId: 'habit-2',
        date: '2024-01-15',
        completedAt: '2024-01-15T10:00:00.000Z',
        completionIndex: 0,
      };

      mockStorageService.getSecureItem.mockImplementation((key) => {
        if (key === 'habits') {
          return Promise.resolve([mockMultipleDailyHabit]);
        }
        if (key === 'completions') {
          return Promise.resolve([existingCompletion]);
        }
        return Promise.resolve([]);
      });

      const completion = await habitService.completeHabit('habit-2');

      expect(completion).toMatchObject({
        habitId: 'habit-2',
        date: '2024-01-15',
        completionIndex: 1,
      });
    });

    it('should not exceed daily target for multiple daily habits', async () => {
      const existingCompletions: HabitCompletion[] = [
        {
          id: 'completion-1',
          habitId: 'habit-2',
          date: '2024-01-15',
          completedAt: '2024-01-15T10:00:00.000Z',
          completionIndex: 0,
        },
        {
          id: 'completion-2',
          habitId: 'habit-2',
          date: '2024-01-15',
          completedAt: '2024-01-15T12:00:00.000Z',
          completionIndex: 1,
        },
        {
          id: 'completion-3',
          habitId: 'habit-2',
          date: '2024-01-15',
          completedAt: '2024-01-15T14:00:00.000Z',
          completionIndex: 2,
        },
      ];

      mockStorageService.getSecureItem.mockImplementation((key) => {
        if (key === 'habits') {
          return Promise.resolve([mockMultipleDailyHabit]);
        }
        if (key === 'completions') {
          return Promise.resolve(existingCompletions);
        }
        return Promise.resolve([]);
      });

      const completion = await habitService.completeHabit('habit-2');

      // Should return the last completion, not create a new one
      expect(completion).toEqual(existingCompletions[2]);
      expect(mockStorageService.setSecureItem).not.toHaveBeenCalled();
    });
  });

  describe('getTodayCompletionCount', () => {
    it('should return correct completion count for multiple daily habits', async () => {
      const completions: HabitCompletion[] = [
        {
          id: 'completion-1',
          habitId: 'habit-1',
          date: '2024-01-15',
          completedAt: '2024-01-15T10:00:00.000Z',
          completionIndex: 0,
        },
        {
          id: 'completion-2',
          habitId: 'habit-1',
          date: '2024-01-15',
          completedAt: '2024-01-15T12:00:00.000Z',
          completionIndex: 1,
        },
        {
          id: 'completion-3',
          habitId: 'habit-2',
          date: '2024-01-15',
          completedAt: '2024-01-15T14:00:00.000Z',
          completionIndex: 0,
        },
      ];

      mockStorageService.getSecureItem.mockResolvedValue(completions);

      const count = await habitService.getTodayCompletionCount('habit-1');
      expect(count).toBe(2);

      const count2 = await habitService.getTodayCompletionCount('habit-2');
      expect(count2).toBe(1);

      const count3 = await habitService.getTodayCompletionCount('habit-3');
      expect(count3).toBe(0);
    });
  });
});
