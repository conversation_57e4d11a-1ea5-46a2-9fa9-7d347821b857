import { t, tSync, getCurrentLanguage, setLanguage, getSupportedLanguages } from '../../utils/i18n';
import { storageService } from '../../services/storage';

// Mock the storage service
jest.mock('../../services/storage', () => ({
  storageService: {
    getSecureItem: jest.fn(),
    setSecureItem: jest.fn(),
  },
}));

// Mock expo-localization
jest.mock('expo-localization', () => ({
  getLocales: jest.fn(),
}));

import { getLocales } from 'expo-localization';

const mockStorageService = storageService as jest.Mocked<typeof storageService>;
const mockGetLocales = getLocales as jest.MockedFunction<typeof getLocales>;

describe('i18n utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getCurrentLanguage', () => {
    it('should return language from storage if available', async () => {
      mockStorageService.getSecureItem.mockResolvedValue({
        language: 'es',
        theme: 'system',
        notificationsEnabled: true,
        defaultReminderTime: '09:00',
        isPremium: false,
        onboardingCompleted: true,
      });

      const language = await getCurrentLanguage();
      expect(language).toBe('es');
    });

    it('should return device language if supported and no storage setting', async () => {
      mockStorageService.getSecureItem.mockResolvedValue(null);
      mockGetLocales.mockReturnValue([
        { languageCode: 'es', regionCode: 'ES' } as any,
      ]);

      const language = await getCurrentLanguage();
      expect(language).toBe('es');
    });

    it('should return fallback language if device language not supported', async () => {
      mockStorageService.getSecureItem.mockResolvedValue(null);
      mockGetLocales.mockReturnValue([
        { languageCode: 'fr', regionCode: 'FR' } as any,
      ]);

      const language = await getCurrentLanguage();
      expect(language).toBe('en');
    });

    it('should return fallback language if storage fails', async () => {
      mockStorageService.getSecureItem.mockRejectedValue(new Error('Storage error'));
      mockGetLocales.mockReturnValue([
        { languageCode: 'fr', regionCode: 'FR' } as any,
      ]);

      const language = await getCurrentLanguage();
      expect(language).toBe('en');
    });
  });

  describe('t (async translation)', () => {
    beforeEach(() => {
      mockStorageService.getSecureItem.mockResolvedValue({
        language: 'en',
        theme: 'system',
        notificationsEnabled: true,
        defaultReminderTime: '09:00',
        isPremium: false,
        onboardingCompleted: true,
      });
    });

    it('should return correct translation for valid key', async () => {
      const translation = await t('app.name');
      expect(translation).toBe('Habiit');
    });

    it('should return translation with parameters', async () => {
      const translation = await t('progress.completedCount', { completed: 3, total: 5 });
      expect(translation).toBe('3 of 5 habits completed');
    });

    it('should return key if translation not found', async () => {
      const translation = await t('non.existent.key');
      expect(translation).toBe('non.existent.key');
    });

    it('should fallback to English if translation not found in current language', async () => {
      mockStorageService.getSecureItem.mockResolvedValue({
        language: 'es',
        theme: 'system',
        notificationsEnabled: true,
        defaultReminderTime: '09:00',
        isPremium: false,
        onboardingCompleted: true,
      });

      // Test with a key that might not exist in Spanish but exists in English
      const translation = await t('app.name');
      expect(translation).toBe('Habiit'); // Should be same in both languages
    });
  });

  describe('tSync (synchronous translation)', () => {
    it('should return correct translation for valid key in English', () => {
      const translation = tSync('app.name', 'en');
      expect(translation).toBe('Habiit');
    });

    it('should return correct translation for valid key in Spanish', () => {
      const translation = tSync('navigation.today', 'es');
      expect(translation).toBe('Hoy');
    });

    it('should return translation with parameters', () => {
      const translation = tSync('progress.completedCount', 'en', { completed: 2, total: 4 });
      expect(translation).toBe('2 of 4 habits completed');
    });

    it('should fallback to English if translation not found in specified language', () => {
      const translation = tSync('app.name', 'fr'); // Unsupported language
      expect(translation).toBe('Habiit');
    });

    it('should return key if translation not found in any language', () => {
      const translation = tSync('completely.invalid.key', 'en');
      expect(translation).toBe('completely.invalid.key');
    });

    it('should handle array translations (random selection)', () => {
      const translation = tSync('notifications.motivationalMessages', 'en');
      expect(typeof translation).toBe('string');
      expect(translation.length).toBeGreaterThan(0);
    });
  });

  describe('setLanguage', () => {
    it('should save language to storage', async () => {
      const existingSettings = {
        theme: 'dark',
        language: 'en',
        notificationsEnabled: true,
        defaultReminderTime: '09:00',
        isPremium: false,
        onboardingCompleted: true,
      };

      mockStorageService.getSecureItem.mockResolvedValue(existingSettings);
      mockStorageService.setSecureItem.mockResolvedValue();

      await setLanguage('es');

      expect(mockStorageService.setSecureItem).toHaveBeenCalledWith(
        'settings',
        {
          ...existingSettings,
          language: 'es',
        }
      );
    });

    it('should create default settings if none exist', async () => {
      mockStorageService.getSecureItem.mockResolvedValue(null);
      mockStorageService.setSecureItem.mockResolvedValue();

      await setLanguage('es');

      expect(mockStorageService.setSecureItem).toHaveBeenCalledWith(
        'settings',
        {
          theme: 'system',
          language: 'es',
          notificationsEnabled: true,
          defaultReminderTime: '09:00',
          isPremium: false,
          onboardingCompleted: false,
        }
      );
    });

    it('should warn and not save unsupported language', async () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      await setLanguage('fr'); // Unsupported language

      expect(consoleSpy).toHaveBeenCalledWith('Unsupported language: fr');
      expect(mockStorageService.setSecureItem).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('should throw error if storage fails', async () => {
      mockStorageService.getSecureItem.mockResolvedValue(null);
      mockStorageService.setSecureItem.mockRejectedValue(new Error('Storage error'));

      await expect(setLanguage('es')).rejects.toThrow('Failed to set language');
    });
  });

  describe('getSupportedLanguages', () => {
    it('should return list of supported languages', () => {
      const languages = getSupportedLanguages();

      expect(languages).toEqual([
        { code: 'en', name: 'English', nativeName: 'English' },
        { code: 'es', name: 'Spanish', nativeName: 'Español' },
      ]);
    });
  });

  describe('nested key access', () => {
    it('should handle deeply nested translation keys', async () => {
      mockStorageService.getSecureItem.mockResolvedValue({
        language: 'en',
        theme: 'system',
        notificationsEnabled: true,
        defaultReminderTime: '09:00',
        isPremium: false,
        onboardingCompleted: true,
      });

      const translation = await t('settings.dataPrivacy');
      expect(translation).toBe('Data & Privacy');
    });

    it('should handle nested keys with parameters', async () => {
      mockStorageService.getSecureItem.mockResolvedValue({
        language: 'en',
        theme: 'system',
        notificationsEnabled: true,
        defaultReminderTime: '09:00',
        isPremium: false,
        onboardingCompleted: true,
      });

      const translation = await t('notifications.streakMessage', { title: 'Exercise', count: 7 });
      expect(translation).toBe("You've completed Exercise for 7 days in a row!");
    });
  });

  describe('error handling', () => {
    it('should handle translation errors gracefully', async () => {
      mockStorageService.getSecureItem.mockRejectedValue(new Error('Storage error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const translation = await t('app.name');
      expect(translation).toBe('app.name'); // Should return key as fallback

      consoleSpy.mockRestore();
    });

    it('should handle sync translation errors gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // Force an error by passing invalid data
      const translation = tSync('app.name', null as any);
      expect(translation).toBe('app.name'); // Should return key as fallback

      consoleSpy.mockRestore();
    });
  });
});
