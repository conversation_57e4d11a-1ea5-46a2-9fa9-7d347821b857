import { habitService } from '../../services/habitService';
import { storageService } from '../../services/storage';
import { Habit, HabitFormData } from '../../types/habit';
import { STORAGE_KEYS } from '../../types/app';

// Mock the storage service
jest.mock('../../services/storage', () => ({
  storageService: {
    getSecureItem: jest.fn(),
    setSecureItem: jest.fn(),
    initialize: jest.fn(),
  },
}));

// Mock the notification service import
jest.mock('../../services/notificationService', () => ({
  notificationService: {
    scheduleHabitReminder: jest.fn(),
    cancelHabitReminders: jest.fn(),
    sendStreakCelebration: jest.fn(),
  },
}));

const mockStorageService = storageService as jest.Mocked<typeof storageService>;

describe('HabitService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset Date.now for consistent IDs
    jest.spyOn(Date, 'now').mockReturnValue(1234567890000);
    jest.spyOn(Math, 'random').mockReturnValue(0.5);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('createHabit', () => {
    it('should create a new habit successfully', async () => {
      const mockHabits: Habit[] = [];
      mockStorageService.getSecureItem.mockResolvedValue(mockHabits);
      mockStorageService.setSecureItem.mockResolvedValue();

      const formData: HabitFormData = {
        title: 'Test Habit',
        emoji: '💪',
        color: '#FF6B6B',
        frequency: 'daily',
        reminderEnabled: false,
      };

      const result = await habitService.createHabit(formData);

      expect(result).toMatchObject({
        title: 'Test Habit',
        emoji: '💪',
        color: '#FF6B6B',
        frequency: 'daily',
        reminderEnabled: false,
        isActive: true,
      });

      expect(result.id).toBeDefined();
      expect(result.createdAt).toBeDefined();
      expect(result.updatedAt).toBeDefined();

      expect(mockStorageService.getSecureItem).toHaveBeenCalledWith(STORAGE_KEYS.HABITS);
      expect(mockStorageService.setSecureItem).toHaveBeenCalledWith(
        STORAGE_KEYS.HABITS,
        expect.arrayContaining([result])
      );
    });

    it('should handle existing habits when creating new one', async () => {
      const existingHabit: Habit = {
        id: 'existing-id',
        title: 'Existing Habit',
        color: '#10B981',
        frequency: 'daily',
        reminderEnabled: false,
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
        isActive: true,
      };

      mockStorageService.getSecureItem.mockResolvedValue([existingHabit]);
      mockStorageService.setSecureItem.mockResolvedValue();

      const formData: HabitFormData = {
        title: 'New Habit',
        color: '#FF6B6B',
        frequency: 'weekly',
        reminderEnabled: true,
        reminderTime: '09:00',
      };

      const result = await habitService.createHabit(formData);

      expect(mockStorageService.setSecureItem).toHaveBeenCalledWith(
        STORAGE_KEYS.HABITS,
        expect.arrayContaining([existingHabit, result])
      );
    });
  });

  describe('updateHabit', () => {
    it('should update an existing habit successfully', async () => {
      const existingHabit: Habit = {
        id: 'test-id',
        title: 'Original Title',
        color: '#10B981',
        frequency: 'daily',
        reminderEnabled: false,
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
        isActive: true,
      };

      mockStorageService.getSecureItem.mockResolvedValue([existingHabit]);
      mockStorageService.setSecureItem.mockResolvedValue();

      const updateData: Partial<HabitFormData> = {
        title: 'Updated Title',
        reminderEnabled: true,
      };

      const result = await habitService.updateHabit('test-id', updateData);

      expect(result.title).toBe('Updated Title');
      expect(result.reminderEnabled).toBe(true);
      expect(result.color).toBe('#10B981'); // Should preserve unchanged fields
      expect(result.updatedAt).not.toBe(existingHabit.updatedAt); // Should update timestamp

      expect(mockStorageService.setSecureItem).toHaveBeenCalledWith(
        STORAGE_KEYS.HABITS,
        expect.arrayContaining([result])
      );
    });

    it('should throw error when habit not found', async () => {
      mockStorageService.getSecureItem.mockResolvedValue([]);

      await expect(
        habitService.updateHabit('non-existent-id', { title: 'New Title' })
      ).rejects.toThrow('Habit not found');
    });
  });

  describe('deleteHabit', () => {
    it('should delete habit and its completions', async () => {
      const habit: Habit = {
        id: 'test-id',
        title: 'Test Habit',
        color: '#FF6B6B',
        frequency: 'daily',
        reminderEnabled: false,
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
        isActive: true,
      };

      const completions = [
        {
          id: 'completion-1',
          habitId: 'test-id',
          date: '2023-01-01',
          completedAt: '2023-01-01T10:00:00.000Z',
        },
        {
          id: 'completion-2',
          habitId: 'other-id',
          date: '2023-01-01',
          completedAt: '2023-01-01T10:00:00.000Z',
        },
      ];

      mockStorageService.getSecureItem
        .mockResolvedValueOnce([habit]) // For habits
        .mockResolvedValueOnce(completions); // For completions

      mockStorageService.setSecureItem.mockResolvedValue();

      await habitService.deleteHabit('test-id');

      expect(mockStorageService.setSecureItem).toHaveBeenCalledWith(
        STORAGE_KEYS.HABITS,
        []
      );

      expect(mockStorageService.setSecureItem).toHaveBeenCalledWith(
        STORAGE_KEYS.COMPLETIONS,
        [completions[1]] // Should only keep completions for other habits
      );
    });
  });

  describe('completeHabit', () => {
    it('should mark habit as completed for specific date', async () => {
      const completions = [];
      mockStorageService.getSecureItem.mockResolvedValue(completions);
      mockStorageService.setSecureItem.mockResolvedValue();

      const testDate = new Date('2023-01-01T15:30:00.000Z');
      const result = await habitService.completeHabit('test-id', testDate);

      expect(result).toMatchObject({
        habitId: 'test-id',
        date: '2023-01-01',
      });

      expect(result.id).toBeDefined();
      expect(result.completedAt).toBeDefined();

      expect(mockStorageService.setSecureItem).toHaveBeenCalledWith(
        STORAGE_KEYS.COMPLETIONS,
        expect.arrayContaining([result])
      );
    });

    it('should return existing completion if already completed', async () => {
      const existingCompletion = {
        id: 'existing-completion',
        habitId: 'test-id',
        date: '2023-01-01',
        completedAt: '2023-01-01T10:00:00.000Z',
      };

      mockStorageService.getSecureItem.mockResolvedValue([existingCompletion]);

      const testDate = new Date('2023-01-01T15:30:00.000Z');
      const result = await habitService.completeHabit('test-id', testDate);

      expect(result).toBe(existingCompletion);
      expect(mockStorageService.setSecureItem).not.toHaveBeenCalled();
    });
  });

  describe('calculateStreak', () => {
    it('should calculate current streak correctly', async () => {
      const completions = [
        {
          id: '1',
          habitId: 'test-id',
          date: '2023-01-05', // Today
          completedAt: '2023-01-05T10:00:00.000Z',
        },
        {
          id: '2',
          habitId: 'test-id',
          date: '2023-01-04', // Yesterday
          completedAt: '2023-01-04T10:00:00.000Z',
        },
        {
          id: '3',
          habitId: 'test-id',
          date: '2023-01-03', // Day before yesterday
          completedAt: '2023-01-03T10:00:00.000Z',
        },
        {
          id: '4',
          habitId: 'test-id',
          date: '2023-01-01', // Gap - not consecutive
          completedAt: '2023-01-01T10:00:00.000Z',
        },
      ];

      mockStorageService.getSecureItem.mockResolvedValue(completions);

      // Mock today's date
      jest.spyOn(Date, 'now').mockReturnValue(new Date('2023-01-05T12:00:00.000Z').getTime());

      const result = await habitService.calculateStreak('test-id');

      expect(result.currentStreak).toBe(3); // 3 consecutive days
      expect(result.longestStreak).toBe(3);
      expect(result.lastCompletedDate).toBe('2023-01-05');
    });

    it('should return zero streak for no completions', async () => {
      mockStorageService.getSecureItem.mockResolvedValue([]);

      const result = await habitService.calculateStreak('test-id');

      expect(result.currentStreak).toBe(0);
      expect(result.longestStreak).toBe(0);
      expect(result.lastCompletedDate).toBeUndefined();
    });
  });

  describe('isHabitCompleted', () => {
    it('should return true if habit is completed for date', async () => {
      const completions = [
        {
          id: '1',
          habitId: 'test-id',
          date: '2023-01-01',
          completedAt: '2023-01-01T10:00:00.000Z',
        },
      ];

      mockStorageService.getSecureItem.mockResolvedValue(completions);

      const testDate = new Date('2023-01-01T15:30:00.000Z');
      const result = await habitService.isHabitCompleted('test-id', testDate);

      expect(result).toBe(true);
    });

    it('should return false if habit is not completed for date', async () => {
      const completions = [
        {
          id: '1',
          habitId: 'test-id',
          date: '2023-01-02',
          completedAt: '2023-01-02T10:00:00.000Z',
        },
      ];

      mockStorageService.getSecureItem.mockResolvedValue(completions);

      const testDate = new Date('2023-01-01T15:30:00.000Z');
      const result = await habitService.isHabitCompleted('test-id', testDate);

      expect(result).toBe(false);
    });
  });
});
