import { storageService } from '../../services/storage';
import { STORAGE_KEYS } from '../../types/app';

// Mock expo-secure-store
jest.mock('expo-secure-store', () => ({
  getItemAsync: jest.fn(),
  setItemAsync: jest.fn(),
  deleteItemAsync: jest.fn(),
}));

// Mock @react-native-async-storage/async-storage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

// Mock expo-crypto
jest.mock('expo-crypto', () => ({
  getRandomBytesAsync: jest.fn(),
}));

// Mock react-native Platform
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios',
  },
}));

import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Crypto from 'expo-crypto';

const mockSecureStore = SecureStore as jest.Mocked<typeof SecureStore>;
const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;
const mockCrypto = Crypto as jest.Mocked<typeof Crypto>;

describe('StorageService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('initialize', () => {
    it('should initialize with existing encryption key', async () => {
      const existingKey = 'existing-encryption-key';
      mockSecureStore.getItemAsync.mockResolvedValue(existingKey);

      await storageService.initialize();

      expect(mockSecureStore.getItemAsync).toHaveBeenCalledWith('encryption_key');
      expect(mockSecureStore.setItemAsync).not.toHaveBeenCalled();
    });

    it('should generate new encryption key if none exists', async () => {
      mockSecureStore.getItemAsync.mockResolvedValue(null);
      mockCrypto.getRandomBytesAsync.mockResolvedValue(new Uint8Array([1, 2, 3, 4]));

      await storageService.initialize();

      expect(mockSecureStore.getItemAsync).toHaveBeenCalledWith('encryption_key');
      expect(mockCrypto.getRandomBytesAsync).toHaveBeenCalledWith(32);
      expect(mockSecureStore.setItemAsync).toHaveBeenCalledWith(
        'encryption_key',
        expect.any(String)
      );
    });

    it('should throw error if initialization fails', async () => {
      mockSecureStore.getItemAsync.mockRejectedValue(new Error('Storage error'));

      await expect(storageService.initialize()).rejects.toThrow('Storage initialization failed');
    });
  });

  describe('setSecureItem', () => {
    beforeEach(async () => {
      // Initialize storage service first
      mockSecureStore.getItemAsync.mockResolvedValue('test-encryption-key');
      await storageService.initialize();
      jest.clearAllMocks();
    });

    it('should store encrypted data using SecureStore on native platforms', async () => {
      const testData = { test: 'data' };
      mockSecureStore.setItemAsync.mockResolvedValue();

      await storageService.setSecureItem(STORAGE_KEYS.HABITS, testData);

      expect(mockSecureStore.setItemAsync).toHaveBeenCalledWith(
        STORAGE_KEYS.HABITS,
        expect.any(String)
      );
      expect(mockAsyncStorage.setItem).not.toHaveBeenCalled();
    });

    it('should handle storage errors gracefully', async () => {
      const testData = { test: 'data' };
      mockSecureStore.setItemAsync.mockRejectedValue(new Error('Storage error'));

      await expect(
        storageService.setSecureItem(STORAGE_KEYS.HABITS, testData)
      ).rejects.toThrow(`Storage operation failed for ${STORAGE_KEYS.HABITS}`);
    });
  });

  describe('getSecureItem', () => {
    beforeEach(async () => {
      // Initialize storage service first
      mockSecureStore.getItemAsync.mockResolvedValue('test-encryption-key');
      await storageService.initialize();
      jest.clearAllMocks();
    });

    it('should retrieve and decrypt data from SecureStore', async () => {
      const testData = { test: 'data' };
      // Mock encrypted data (this would be the actual encrypted string in real usage)
      const encryptedData = Buffer.from('test-encryption-key' + JSON.stringify(testData)).toString('base64');
      mockSecureStore.getItemAsync.mockResolvedValue(encryptedData);

      const result = await storageService.getSecureItem(STORAGE_KEYS.HABITS);

      expect(result).toEqual(testData);
      expect(mockSecureStore.getItemAsync).toHaveBeenCalledWith(STORAGE_KEYS.HABITS);
    });

    it('should return null if no data exists', async () => {
      mockSecureStore.getItemAsync.mockResolvedValue(null);

      const result = await storageService.getSecureItem(STORAGE_KEYS.HABITS);

      expect(result).toBeNull();
    });

    it('should return null if decryption fails', async () => {
      mockSecureStore.getItemAsync.mockResolvedValue('invalid-encrypted-data');

      const result = await storageService.getSecureItem(STORAGE_KEYS.HABITS);

      expect(result).toBeNull();
    });
  });

  describe('removeSecureItem', () => {
    beforeEach(async () => {
      // Initialize storage service first
      mockSecureStore.getItemAsync.mockResolvedValue('test-encryption-key');
      await storageService.initialize();
      jest.clearAllMocks();
    });

    it('should remove item from SecureStore', async () => {
      mockSecureStore.deleteItemAsync.mockResolvedValue();

      await storageService.removeSecureItem(STORAGE_KEYS.HABITS);

      expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith(STORAGE_KEYS.HABITS);
    });

    it('should handle removal errors gracefully', async () => {
      mockSecureStore.deleteItemAsync.mockRejectedValue(new Error('Delete error'));

      await expect(
        storageService.removeSecureItem(STORAGE_KEYS.HABITS)
      ).rejects.toThrow(`Remove operation failed for ${STORAGE_KEYS.HABITS}`);
    });
  });

  describe('clearAllData', () => {
    beforeEach(async () => {
      // Initialize storage service first
      mockSecureStore.getItemAsync.mockResolvedValue('test-encryption-key');
      await storageService.initialize();
      jest.clearAllMocks();
    });

    it('should clear all app data and encryption key', async () => {
      mockSecureStore.deleteItemAsync.mockResolvedValue();

      await storageService.clearAllData();

      // Should delete all storage keys
      Object.values(STORAGE_KEYS).forEach(key => {
        expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith(key);
      });

      // Should also delete encryption key
      expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith('encryption_key');
    });

    it('should handle clear errors gracefully', async () => {
      mockSecureStore.deleteItemAsync.mockRejectedValue(new Error('Clear error'));

      await expect(storageService.clearAllData()).rejects.toThrow('Data clearing failed');
    });
  });

  describe('exportData', () => {
    beforeEach(async () => {
      // Initialize storage service first
      mockSecureStore.getItemAsync.mockResolvedValue('test-encryption-key');
      await storageService.initialize();
      jest.clearAllMocks();
    });

    it('should export all data as JSON string', async () => {
      const testHabits = [{ id: '1', title: 'Test Habit' }];
      const testSettings = { theme: 'dark' };

      // Mock encrypted data
      const encryptedHabits = Buffer.from('test-encryption-key' + JSON.stringify(testHabits)).toString('base64');
      const encryptedSettings = Buffer.from('test-encryption-key' + JSON.stringify(testSettings)).toString('base64');

      mockSecureStore.getItemAsync
        .mockImplementation((key: string) => {
          switch (key) {
            case STORAGE_KEYS.HABITS:
              return Promise.resolve(encryptedHabits);
            case STORAGE_KEYS.SETTINGS:
              return Promise.resolve(encryptedSettings);
            default:
              return Promise.resolve(null);
          }
        });

      const result = await storageService.exportData();

      const exportedData = JSON.parse(result);
      expect(exportedData[STORAGE_KEYS.HABITS]).toEqual(testHabits);
      expect(exportedData[STORAGE_KEYS.SETTINGS]).toEqual(testSettings);
    });

    it('should handle export errors gracefully', async () => {
      mockSecureStore.getItemAsync.mockRejectedValue(new Error('Export error'));

      await expect(storageService.exportData()).rejects.toThrow('Data export failed');
    });
  });

  describe('web platform behavior', () => {
    beforeEach(() => {
      // Mock Platform.OS to be 'web'
      jest.doMock('react-native', () => ({
        Platform: {
          OS: 'web',
        },
      }));
    });

    afterEach(() => {
      jest.dontMock('react-native');
    });

    it('should use AsyncStorage on web platform', async () => {
      // Re-import to get the mocked Platform
      jest.resetModules();
      const { storageService: webStorageService } = require('../../services/storage');

      mockAsyncStorage.getItem.mockResolvedValue('test-encryption-key');
      await webStorageService.initialize();
      jest.clearAllMocks();

      const testData = { test: 'data' };
      mockAsyncStorage.setItem.mockResolvedValue();

      await webStorageService.setSecureItem(STORAGE_KEYS.HABITS, testData);

      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        STORAGE_KEYS.HABITS,
        expect.any(String)
      );
      expect(mockSecureStore.setItemAsync).not.toHaveBeenCalled();
    });
  });
});
