import { storageService } from '../services/storage';
import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Mock the dependencies
jest.mock('expo-secure-store');
jest.mock('@react-native-async-storage/async-storage');
jest.mock('expo-crypto', () => ({
  getRandomBytesAsync: jest.fn().mockResolvedValue(new Uint8Array(32).fill(1)),
}));

const mockSecureStore = SecureStore as jest.Mocked<typeof SecureStore>;
const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

describe('StorageService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the service state
    (storageService as any).encryptionKey = null;
  });

  describe('initialization', () => {
    it('should initialize with existing encryption key on native platforms', async () => {
      Platform.OS = 'ios';
      mockSecureStore.getItemAsync.mockResolvedValue('existing-key');

      await storageService.initialize();

      expect(mockSecureStore.getItemAsync).toHaveBeenCalledWith('encryption_key');
      expect(mockSecureStore.setItemAsync).not.toHaveBeenCalled();
    });

    it('should generate new encryption key if none exists on native platforms', async () => {
      Platform.OS = 'ios';
      mockSecureStore.getItemAsync.mockResolvedValue(null);

      await storageService.initialize();

      expect(mockSecureStore.getItemAsync).toHaveBeenCalledWith('encryption_key');
      expect(mockSecureStore.setItemAsync).toHaveBeenCalledWith(
        'encryption_key',
        expect.any(String)
      );
    });

    it('should initialize with AsyncStorage on web platform', async () => {
      Platform.OS = 'web';
      mockAsyncStorage.getItem.mockResolvedValue('existing-key');

      await storageService.initialize();

      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('encryption_key');
      expect(mockAsyncStorage.setItem).not.toHaveBeenCalled();
    });

    it('should generate new encryption key for web if none exists', async () => {
      Platform.OS = 'web';
      mockAsyncStorage.getItem.mockResolvedValue(null);

      await storageService.initialize();

      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('encryption_key');
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'encryption_key',
        expect.any(String)
      );
    });

    it('should throw error if initialization fails', async () => {
      Platform.OS = 'ios';
      mockSecureStore.getItemAsync.mockRejectedValue(new Error('Storage error'));

      await expect(storageService.initialize()).rejects.toThrow('Storage initialization failed');
    });
  });

  describe('secure item operations', () => {
    beforeEach(async () => {
      Platform.OS = 'ios';
      mockSecureStore.getItemAsync.mockResolvedValue('test-key');
      await storageService.initialize();
    });

    it('should store and retrieve secure items on native platforms', async () => {
      const testData = { name: 'Test Habit', completed: true };
      mockSecureStore.setItemAsync.mockResolvedValue();
      mockSecureStore.getItemAsync.mockResolvedValue('dGVzdC1rZXl7Im5hbWUiOiJUZXN0IEhhYml0IiwiY29tcGxldGVkIjp0cnVlfQ==');

      await storageService.setSecureItem('test-key', testData);
      const retrieved = await storageService.getSecureItem('test-key');

      expect(mockSecureStore.setItemAsync).toHaveBeenCalledWith(
        'test-key',
        expect.any(String)
      );
      expect(retrieved).toEqual(testData);
    });

    it('should store and retrieve secure items on web platform', async () => {
      Platform.OS = 'web';
      // Re-initialize for web
      (storageService as any).encryptionKey = null;
      mockAsyncStorage.getItem.mockResolvedValue('test-key');
      await storageService.initialize();

      const testData = { name: 'Test Habit', completed: true };
      mockAsyncStorage.setItem.mockResolvedValue();
      mockAsyncStorage.getItem.mockResolvedValue('dGVzdC1rZXl7Im5hbWUiOiJUZXN0IEhhYml0IiwiY29tcGxldGVkIjp0cnVlfQ==');

      await storageService.setSecureItem('test-key', testData);
      const retrieved = await storageService.getSecureItem('test-key');

      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'test-key',
        expect.any(String)
      );
      expect(retrieved).toEqual(testData);
    });

    it('should return null for non-existent items', async () => {
      mockSecureStore.getItemAsync.mockResolvedValue(null);

      const retrieved = await storageService.getSecureItem('non-existent');

      expect(retrieved).toBeNull();
    });

    it('should handle decryption errors gracefully', async () => {
      mockSecureStore.getItemAsync.mockResolvedValue('invalid-encrypted-data');

      const retrieved = await storageService.getSecureItem('test-key');

      expect(retrieved).toBeNull();
    });

    it('should remove secure items on native platforms', async () => {
      mockSecureStore.deleteItemAsync.mockResolvedValue();

      await storageService.removeSecureItem('test-key');

      expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith('test-key');
    });

    it('should remove secure items on web platform', async () => {
      Platform.OS = 'web';
      mockAsyncStorage.removeItem.mockResolvedValue();

      await storageService.removeSecureItem('test-key');

      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('test-key');
    });
  });

  describe('data management', () => {
    beforeEach(async () => {
      Platform.OS = 'ios';
      mockSecureStore.getItemAsync.mockResolvedValue('test-key');
      await storageService.initialize();
    });

    it('should clear all data on native platforms', async () => {
      mockSecureStore.deleteItemAsync.mockResolvedValue();

      await storageService.clearAllData();

      // Should delete all storage keys plus the encryption key
      expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledTimes(5); // 4 storage keys + encryption key
      expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith('encryption_key');
    });

    it('should clear all data on web platform', async () => {
      Platform.OS = 'web';
      // Re-initialize for web
      (storageService as any).encryptionKey = null;
      mockAsyncStorage.getItem.mockResolvedValue('test-key');
      await storageService.initialize();

      mockAsyncStorage.removeItem.mockResolvedValue();

      await storageService.clearAllData();

      // Should delete all storage keys plus the encryption key
      expect(mockAsyncStorage.removeItem).toHaveBeenCalledTimes(5); // 4 storage keys + encryption key
      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('encryption_key');
    });

    it('should export data successfully', async () => {
      const mockHabits = [{ id: '1', title: 'Test Habit' }];
      const mockCompletions = [{ id: '1', habitId: '1', date: '2024-01-15' }];

      // Mock getSecureItem to return different data based on key
      jest.spyOn(storageService, 'getSecureItem').mockImplementation((key) => {
        if (key === 'habits') return Promise.resolve(mockHabits);
        if (key === 'completions') return Promise.resolve(mockCompletions);
        return Promise.resolve(null);
      });

      const exportedData = await storageService.exportData();
      const parsedData = JSON.parse(exportedData);

      expect(parsedData).toHaveProperty('habits', mockHabits);
      expect(parsedData).toHaveProperty('completions', mockCompletions);
    });

    it('should handle export errors gracefully', async () => {
      jest.spyOn(storageService, 'getSecureItem').mockRejectedValue(new Error('Storage error'));

      await expect(storageService.exportData()).rejects.toThrow('Data export failed');
    });
  });

  describe('error handling', () => {
    it('should throw error when trying to encrypt without initialization', async () => {
      // Don't initialize the service
      const testData = { test: 'data' };

      await expect(storageService.setSecureItem('test-key', testData))
        .rejects.toThrow('Storage operation failed for test-key');
    });

    it('should handle storage operation failures', async () => {
      Platform.OS = 'ios';
      mockSecureStore.getItemAsync.mockResolvedValue('test-key');
      await storageService.initialize();

      mockSecureStore.setItemAsync.mockRejectedValue(new Error('Storage full'));

      await expect(storageService.setSecureItem('test-key', { test: 'data' }))
        .rejects.toThrow('Storage operation failed for test-key');
    });

    it('should handle clear data failures', async () => {
      Platform.OS = 'ios';
      mockSecureStore.getItemAsync.mockResolvedValue('test-key');
      await storageService.initialize();

      mockSecureStore.deleteItemAsync.mockRejectedValue(new Error('Delete failed'));

      await expect(storageService.clearAllData()).rejects.toThrow('Data clearing failed');
    });
  });
});
