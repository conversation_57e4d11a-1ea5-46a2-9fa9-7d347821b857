import { notificationService } from '../services/notificationService';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { Habit } from '../types/habit';

// Mock expo-notifications
jest.mock('expo-notifications');
const mockNotifications = Notifications as jest.Mocked<typeof Notifications>;

// Mock Platform
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios',
  },
}));

describe('NotificationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset initialization state
    (notificationService as any).isInitialized = false;
  });

  describe('initialization', () => {
    it('should initialize successfully on native platforms', async () => {
      Platform.OS = 'ios';
      mockNotifications.setNotificationHandler.mockImplementation(() => {});
      mockNotifications.getPermissionsAsync.mockResolvedValue({ status: 'granted' } as any);

      await notificationService.initialize();

      expect(mockNotifications.setNotificationHandler).toHaveBeenCalled();
      expect(mockNotifications.getPermissionsAsync).toHaveBeenCalled();
    });

    it('should skip initialization on web platform', async () => {
      Platform.OS = 'web';

      await notificationService.initialize();

      expect(mockNotifications.setNotificationHandler).not.toHaveBeenCalled();
    });

    it('should request permissions if not granted', async () => {
      Platform.OS = 'ios';
      mockNotifications.setNotificationHandler.mockImplementation(() => {});
      mockNotifications.getPermissionsAsync.mockResolvedValue({ status: 'denied' } as any);
      mockNotifications.requestPermissionsAsync.mockResolvedValue({ status: 'granted' } as any);

      await notificationService.initialize();

      expect(mockNotifications.requestPermissionsAsync).toHaveBeenCalled();
    });

    it('should create Android notification channel', async () => {
      Platform.OS = 'android';
      mockNotifications.setNotificationHandler.mockImplementation(() => {});
      mockNotifications.getPermissionsAsync.mockResolvedValue({ status: 'granted' } as any);
      mockNotifications.setNotificationChannelAsync.mockResolvedValue({} as any);

      await notificationService.initialize();

      expect(mockNotifications.setNotificationChannelAsync).toHaveBeenCalledWith(
        'habit-reminders',
        expect.objectContaining({
          name: 'Habit Reminders',
          importance: Notifications.AndroidImportance.DEFAULT,
        })
      );
    });

    it('should handle initialization errors gracefully on web', async () => {
      Platform.OS = 'web';
      // Force an error by mocking a function that shouldn't be called on web
      mockNotifications.setNotificationHandler.mockImplementation(() => {
        throw new Error('Not supported on web');
      });

      // Should not throw
      await expect(notificationService.initialize()).resolves.not.toThrow();
    });
  });

  describe('habit reminders', () => {
    const mockHabit: Habit = {
      id: 'habit-1',
      title: 'Drink Water',
      emoji: '💧',
      color: '#4ECDC4',
      frequency: 'daily',
      reminderEnabled: true,
      reminderTime: '09:00',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      isActive: true,
    };

    beforeEach(async () => {
      Platform.OS = 'ios';
      mockNotifications.setNotificationHandler.mockImplementation(() => {});
      mockNotifications.getPermissionsAsync.mockResolvedValue({ status: 'granted' } as any);
      await notificationService.initialize();
    });

    it('should schedule daily habit reminder', async () => {
      mockNotifications.scheduleNotificationAsync.mockResolvedValue('notification-id');

      await notificationService.scheduleHabitReminder(mockHabit, '09:00');

      expect(mockNotifications.scheduleNotificationAsync).toHaveBeenCalledWith({
        content: {
          title: '💧 Time for Drink Water!',
          body: "Don't forget to complete your daily habit",
          data: {
            type: 'habit-reminder',
            habitId: 'habit-1',
            habitTitle: 'Drink Water',
          },
        },
        trigger: {
          hour: 9,
          minute: 0,
          repeats: true,
        },
      });
    });

    it('should schedule multiple daily habit reminders with interval', async () => {
      const multipleDailyHabit: Habit = {
        ...mockHabit,
        frequency: 'multiple_daily',
        dailyTarget: 4,
        reminderInterval: 120, // 2 hours
      };

      mockNotifications.scheduleNotificationAsync.mockResolvedValue('notification-id');

      await notificationService.scheduleHabitReminder(multipleDailyHabit, '08:00');

      // Should schedule 4 notifications (dailyTarget)
      expect(mockNotifications.scheduleNotificationAsync).toHaveBeenCalledTimes(4);

      // Check first notification
      expect(mockNotifications.scheduleNotificationAsync).toHaveBeenNthCalledWith(1, {
        content: {
          title: '💧 Time for Drink Water! (1/4)',
          body: "Complete your habit - 3 more to go today!",
          data: {
            type: 'habit-reminder',
            habitId: 'habit-1',
            habitTitle: 'Drink Water',
            completionIndex: 0,
          },
        },
        trigger: {
          hour: 8,
          minute: 0,
          repeats: true,
        },
      });

      // Check second notification (2 hours later)
      expect(mockNotifications.scheduleNotificationAsync).toHaveBeenNthCalledWith(2, {
        content: {
          title: '💧 Time for Drink Water! (2/4)',
          body: "Complete your habit - 2 more to go today!",
          data: {
            type: 'habit-reminder',
            habitId: 'habit-1',
            habitTitle: 'Drink Water',
            completionIndex: 1,
          },
        },
        trigger: {
          hour: 10,
          minute: 0,
          repeats: true,
        },
      });
    });

    it('should schedule multiple daily habit reminders with specific times', async () => {
      const multipleDailyHabit: Habit = {
        ...mockHabit,
        frequency: 'multiple_daily',
        dailyTarget: 3,
        reminderTimes: ['08:00', '14:00', '20:00'],
      };

      mockNotifications.scheduleNotificationAsync.mockResolvedValue('notification-id');

      await notificationService.scheduleHabitReminder(multipleDailyHabit);

      // Should schedule 3 notifications (one for each specific time)
      expect(mockNotifications.scheduleNotificationAsync).toHaveBeenCalledTimes(3);

      // Check first notification at 08:00
      expect(mockNotifications.scheduleNotificationAsync).toHaveBeenNthCalledWith(1, {
        content: {
          title: '💧 Time for Drink Water! (1/3)',
          body: "Complete your habit - 2 more to go today!",
          data: {
            type: 'habit-reminder',
            habitId: 'habit-1',
            habitTitle: 'Drink Water',
            completionIndex: 0,
          },
        },
        trigger: {
          hour: 8,
          minute: 0,
          repeats: true,
        },
      });

      // Check second notification at 14:00
      expect(mockNotifications.scheduleNotificationAsync).toHaveBeenNthCalledWith(2, {
        content: {
          title: '💧 Time for Drink Water! (2/3)',
          body: "Complete your habit - 1 more to go today!",
          data: {
            type: 'habit-reminder',
            habitId: 'habit-1',
            habitTitle: 'Drink Water',
            completionIndex: 1,
          },
        },
        trigger: {
          hour: 14,
          minute: 0,
          repeats: true,
        },
      });
    });

    it('should cancel habit reminders', async () => {
      mockNotifications.cancelScheduledNotificationAsync.mockResolvedValue();
      mockNotifications.getAllScheduledNotificationsAsync.mockResolvedValue([
        {
          identifier: 'notification-1',
          content: {
            data: { type: 'habit-reminder', habitId: 'habit-1' },
          },
        } as any,
        {
          identifier: 'notification-2',
          content: {
            data: { type: 'habit-reminder', habitId: 'habit-2' },
          },
        } as any,
      ]);

      await notificationService.cancelHabitReminders('habit-1');

      expect(mockNotifications.cancelScheduledNotificationAsync).toHaveBeenCalledWith('notification-1');
      expect(mockNotifications.cancelScheduledNotificationAsync).not.toHaveBeenCalledWith('notification-2');
    });
  });

  describe('celebration notifications', () => {
    beforeEach(async () => {
      Platform.OS = 'ios';
      mockNotifications.setNotificationHandler.mockImplementation(() => {});
      mockNotifications.getPermissionsAsync.mockResolvedValue({ status: 'granted' } as any);
      await notificationService.initialize();
    });

    it('should send streak celebration for 7-day streak', async () => {
      mockNotifications.scheduleNotificationAsync.mockResolvedValue('notification-id');

      await notificationService.sendStreakCelebration('Drink Water', 7);

      expect(mockNotifications.scheduleNotificationAsync).toHaveBeenCalledWith({
        content: {
          title: '🔥 One week streak!',
          body: "Amazing! You've kept up with Drink Water for a whole week!",
          data: {
            type: 'streak-celebration',
            streakCount: 7,
            habitTitle: 'Drink Water',
          },
        },
        trigger: null,
      });
    });

    it('should send streak celebration for 30-day streak', async () => {
      mockNotifications.scheduleNotificationAsync.mockResolvedValue('notification-id');

      await notificationService.sendStreakCelebration('Exercise', 30);

      expect(mockNotifications.scheduleNotificationAsync).toHaveBeenCalledWith({
        content: {
          title: '🏆 30-day streak!',
          body: "Incredible! You've maintained Exercise for a full month!",
          data: {
            type: 'streak-celebration',
            streakCount: 30,
            habitTitle: 'Exercise',
          },
        },
        trigger: null,
      });
    });

    it('should send daily target celebration', async () => {
      mockNotifications.scheduleNotificationAsync.mockResolvedValue('notification-id');

      await notificationService.sendDailyTargetCelebration('Drink Water', 8);

      expect(mockNotifications.scheduleNotificationAsync).toHaveBeenCalledWith({
        content: {
          title: '🌟 Amazing dedication!',
          body: "Wow! You completed Drink Water 8 times today. You're on fire!",
          data: {
            type: 'daily-target-celebration',
            targetCount: 8,
            habitTitle: 'Drink Water',
          },
        },
        trigger: null,
      });
    });

    it('should send daily target celebration for smaller targets', async () => {
      mockNotifications.scheduleNotificationAsync.mockResolvedValue('notification-id');

      await notificationService.sendDailyTargetCelebration('Take Medication', 3);

      expect(mockNotifications.scheduleNotificationAsync).toHaveBeenCalledWith({
        content: {
          title: '✅ Daily target reached!',
          body: "Perfect! You've completed Take Medication 3 times today!",
          data: {
            type: 'daily-target-celebration',
            targetCount: 3,
            habitTitle: 'Take Medication',
          },
        },
        trigger: null,
      });
    });
  });

  describe('error handling', () => {
    it('should handle notification scheduling errors gracefully', async () => {
      Platform.OS = 'ios';
      mockNotifications.setNotificationHandler.mockImplementation(() => {});
      mockNotifications.getPermissionsAsync.mockResolvedValue({ status: 'granted' } as any);
      await notificationService.initialize();

      mockNotifications.scheduleNotificationAsync.mockRejectedValue(new Error('Scheduling failed'));

      const mockHabit: Habit = {
        id: 'habit-1',
        title: 'Test Habit',
        color: '#4ECDC4',
        frequency: 'daily',
        reminderEnabled: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        isActive: true,
      };

      // Should not throw
      await expect(notificationService.scheduleHabitReminder(mockHabit, '09:00')).resolves.not.toThrow();
    });

    it('should handle celebration notification errors gracefully', async () => {
      Platform.OS = 'ios';
      mockNotifications.setNotificationHandler.mockImplementation(() => {});
      mockNotifications.getPermissionsAsync.mockResolvedValue({ status: 'granted' } as any);
      await notificationService.initialize();

      mockNotifications.scheduleNotificationAsync.mockRejectedValue(new Error('Celebration failed'));

      // Should not throw
      await expect(notificationService.sendStreakCelebration('Test Habit', 7)).resolves.not.toThrow();
      await expect(notificationService.sendDailyTargetCelebration('Test Habit', 5)).resolves.not.toThrow();
    });
  });
});
