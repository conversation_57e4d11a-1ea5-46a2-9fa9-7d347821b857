{"name": "habiit", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "date-fns": "^4.1.0", "expo": "~53.0.20", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-crypto": "^14.1.5", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-in-app-purchases": "^14.5.0", "expo-linking": "~7.1.7", "expo-localization": "^16.1.6", "expo-notifications": "^0.31.4", "expo-router": "~5.1.4", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-calendars": "^1.1313.0", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.18", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "jest": "^30.0.4", "react-test-renderer": "^19.1.0", "typescript": "~5.8.3"}, "jest": {"preset": "react-native", "setupFilesAfterEnv": ["<rootDir>/src/__tests__/setup.ts"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/__tests__/**", "!src/locales/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json"], "transform": {"^.+\\.(js|jsx|ts|tsx)$": "babel-jest"}, "transformIgnorePatterns": ["node_modules/(?!(react-native|@react-native|expo|@expo|react-native-paper|react-native-vector-icons|@react-navigation)/)"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}}, "private": true}