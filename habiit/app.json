{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "habiit-privacy-first", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "habiit", "userInterfaceStyle": "automatic", "newArchEnabled": true, "description": "Privacy-first habit tracker that keeps all your data on your device. Build better habits without compromising your privacy.", "keywords": ["habits", "productivity", "privacy", "tracking", "goals"], "primaryColor": "#6366F1", "backgroundColor": "#FAFAFA", "ios": {"supportsTablet": true, "bundleIdentifier": "com.habiit.app", "buildNumber": "1", "infoPlist": {"NSUserTrackingUsageDescription": "This app does not track users. This permission is requested by third-party libraries but is not used.", "NSLocationWhenInUseUsageDescription": "This app does not use location services.", "NSCameraUsageDescription": "This app does not use the camera.", "NSMicrophoneUsageDescription": "This app does not use the microphone.", "NSPhotoLibraryUsageDescription": "This app does not access your photo library."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#6366F1"}, "edgeToEdgeEnabled": true, "package": "com.habiit.app", "versionCode": 1, "permissions": ["android.permission.VIBRATE", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.WAKE_LOCK"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#6366F1"}], ["expo-notifications", {"icon": "./assets/images/notification-icon.png", "color": "#6366F1", "defaultChannel": "default"}], "expo-secure-store", "expo-localization"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "1d377fff-61c7-46a2-bdfb-d17fd868e90c"}}, "owner": "maniek5k"}}